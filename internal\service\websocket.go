package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WebSocketManager WebSocket管理器
type WebSocketManager struct {
	logger           *logrus.Logger
	upgrader         websocket.Upgrader
	connections      map[string]*WebSocketConnection
	mutex            sync.RWMutex
	hub              *Hub
	chatService      ChatService                  // 添加聊天服务引用
	aiService        AIService                    // 添加AI服务引用（支持意图识别）
	enhancedHandler  *EnhancedWebSocketHandler    // 增强型WebSocket处理器
	db               *gorm.DB                     // 添加数据库连接用于直接执行
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	ID        string
	UserID    int64
	SessionID string
	Conn      *websocket.Conn
	Send      chan []byte
	Hub       *Hub
}

// WSMessage WebSocket消息
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// Hub WebSocket连接中心
type Hub struct {
	connections map[*WebSocketConnection]bool
	broadcast   chan []byte
	register    chan *WebSocketConnection
	unregister  chan *WebSocketConnection
	logger      *logrus.Logger
}

// SystemNotificationMessage 系统通知消息
type SystemNotificationMessage struct {
	Level     string                 `json:"level"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Actions   []NotificationAction   `json:"actions,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NotificationAction 通知操作
type NotificationAction struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Type  string `json:"type"`
}

// CommandProgressMessage 命令进度消息
type CommandProgressMessage struct {
	Command     string                 `json:"command"`
	HostID      int64                  `json:"host_id"`
	Status      string                 `json:"status"`
	Progress    float64                `json:"progress"`
	Output      string                 `json:"output,omitempty"`
	Error       string                 `json:"error,omitempty"`
	StartTime   time.Time              `json:"start_time"`
	ElapsedTime string                 `json:"elapsed_time"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(logger *logrus.Logger, db *gorm.DB) *WebSocketManager {
	hub := &Hub{
		connections: make(map[*WebSocketConnection]bool),
		broadcast:   make(chan []byte),
		register:    make(chan *WebSocketConnection),
		unregister:  make(chan *WebSocketConnection),
		logger:      logger,
	}

	manager := &WebSocketManager{
		logger:      logger,
		connections: make(map[string]*WebSocketConnection),
		hub:         hub,
		db:          db, // 添加数据库连接
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 启动Hub
	go hub.Run()

	return manager
}

// SetChatService 设置聊天服务
func (wm *WebSocketManager) SetChatService(chatService ChatService) {
	wm.chatService = chatService
}

// SetAIService 设置AI服务
func (wm *WebSocketManager) SetAIService(aiService AIService) {
	wm.aiService = aiService
}

// SetEnhancedHandler 设置增强处理器
func (wm *WebSocketManager) SetEnhancedHandler(enhancedHandler *EnhancedWebSocketHandler) {
	wm.enhancedHandler = enhancedHandler
	wm.logger.WithField("enhanced_handler", fmt.Sprintf("%T", enhancedHandler)).Info("WebSocket: 增强处理器已设置")
}

// HandleWebSocket 处理WebSocket连接
func (wm *WebSocketManager) HandleWebSocket(w http.ResponseWriter, r *http.Request, userID int64, sessionID string) error {
	conn, err := wm.upgrader.Upgrade(w, r, nil)
	if err != nil {
		return fmt.Errorf("failed to upgrade connection: %w", err)
	}

	// 生成连接ID
	connectionID := fmt.Sprintf("%d_%s_%d", userID, sessionID, time.Now().Unix())

	wsConn := &WebSocketConnection{
		ID:        connectionID,
		UserID:    userID,
		SessionID: sessionID,
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Hub:       wm.hub,
	}

	// 注册连接
	wm.mutex.Lock()
	wm.connections[connectionID] = wsConn
	wm.mutex.Unlock()

	wm.hub.register <- wsConn

	wm.logger.WithFields(logrus.Fields{
		"connection_id": connectionID,
		"user_id":       userID,
		"session_id":    sessionID,
	}).Info("WebSocket connection established")

	// 启动读写协程
	go wm.writePump(wsConn)
	go wm.readPump(wsConn)

	return nil
}

// readPump 读取消息
func (wm *WebSocketManager) readPump(conn *WebSocketConnection) {
	defer func() {
		wm.hub.unregister <- conn
		conn.Conn.Close()
		wm.mutex.Lock()
		delete(wm.connections, conn.ID)
		wm.mutex.Unlock()
	}()

	conn.Conn.SetReadLimit(1024) // 增加读取限制
	conn.Conn.SetReadDeadline(time.Now().Add(300 * time.Second)) // 🔧 关键修复：增加到5分钟超时
	conn.Conn.SetPongHandler(func(string) error {
		conn.Conn.SetReadDeadline(time.Now().Add(300 * time.Second)) // 🔧 关键修复：增加到5分钟超时
		return nil
	})

	for {
		_, message, err := conn.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				wm.logger.WithError(err).Error("WebSocket unexpected close error")
			}
			break
		}

		// 先尝试解析为通用的map，以便调试
		var rawMsg map[string]interface{}
		if err := json.Unmarshal(message, &rawMsg); err != nil {
			wm.logger.WithError(err).Error("Failed to unmarshal raw WebSocket message")
			continue
		}

		// 🔧 简化日志：只记录非ping消息
		if msgType, exists := rawMsg["type"]; !exists || msgType != "ping" {
			wm.logger.WithFields(logrus.Fields{
				"connection_id": conn.ID,
				"message_type":  msgType,
				"message_size":  len(message),
			}).Debug("WebSocket: 收到消息")
		}

		var msg WSMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			wm.logger.WithError(err).Error("Failed to unmarshal WebSocket message")
			continue
		}

		// 如果Data为空但原始消息中有内容，尝试提取
		if msg.Data == nil && rawMsg != nil {
			// 检查是否有content字段
			if content, exists := rawMsg["content"]; exists {
				msg.Data = content
				wm.logger.WithField("extracted_content", content).Debug("WebSocket: 提取内容")

				// � 移除快速路径，让所有请求都通过AI处理
			} else if data, exists := rawMsg["data"]; exists {
				msg.Data = data
				// 🔧 简化日志：ping消息不记录data提取
				if msg.Type != "ping" {
					wm.logger.WithField("extracted_data", data).Debug("WebSocket: 提取data")
				}
			}
		}

		wm.handleMessage(conn, &msg)
	}
}

// writePump 写入消息
func (wm *WebSocketManager) writePump(conn *WebSocketConnection) {
	ticker := time.NewTicker(240 * time.Second) // 🔧 关键修复：增加到4分钟心跳间隔
	defer func() {
		ticker.Stop()
		conn.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-conn.Send:
			conn.Conn.SetWriteDeadline(time.Now().Add(60 * time.Second)) // 🔧 关键修复：增加写超时到60秒
			if !ok {
				conn.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// 🔧 修复：直接发送单个JSON消息，避免多消息合并导致解析错误
			if err := conn.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				return
			}
		case <-ticker.C:
			conn.Conn.SetWriteDeadline(time.Now().Add(60 * time.Second)) // 🔧 关键修复：增加写超时到60秒
			if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理消息
func (wm *WebSocketManager) handleMessage(conn *WebSocketConnection, msg *WSMessage) {
	// 🔧 优化日志：过滤常见消息类型，减少日志噪音
	if msg.Type != "ping" && msg.Type != "pong" && msg.Type != "heartbeat" {
		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"message_type":  msg.Type,
			"data_type":     fmt.Sprintf("%T", msg.Data),
		}).Debug("WebSocket: 处理消息") // 改为Debug级别，减少Info日志
	}

	// 根据消息类型处理
	switch msg.Type {
	case "ping":
		// 🔧 修复：传递原始时间戳用于延迟计算
		var responseData interface{} = "pong"
		if msg.Data != nil {
			// 如果ping消息包含时间戳，将其传回
			if dataMap, ok := msg.Data.(map[string]interface{}); ok {
				if timestamp, exists := dataMap["timestamp"]; exists {
					responseData = map[string]interface{}{
						"timestamp": timestamp,
						"server_time": time.Now().UnixMilli(),
					}
				}
			} else if timestamp, ok := msg.Data.(float64); ok {
				// 直接传递时间戳
				responseData = map[string]interface{}{
					"timestamp": timestamp,
					"server_time": time.Now().UnixMilli(),
				}
			}
		}

		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "pong",
			Data:      responseData,
			Timestamp: time.Now(),
		})
	case "heartbeat":
		// 🔧 静默处理心跳消息，不产生日志噪音
		// 心跳消息不需要响应，只需要重置超时时间
	case "stop_generation":
		// 🔧 新增：处理停止生成请求
		wm.handleStopGeneration(conn, *msg)
	case "message":
		// 处理聊天消息
		wm.handleChatMessage(conn, *msg)
	case "subscribe":
		// 处理订阅请求
		wm.handleSubscription(conn, msg.Data)
	case "unsubscribe":
		// 处理取消订阅请求
		wm.handleUnsubscription(conn, msg.Data)
	default:
		wm.logger.WithField("message_type", msg.Type).Warn("Unknown message type")
	}
}

// handleSubscription 处理订阅
func (wm *WebSocketManager) handleSubscription(conn *WebSocketConnection, data interface{}) {
	// 实现订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"subscription":  data,
	}).Debug("Handling subscription")
}

// handleUnsubscription 处理取消订阅
func (wm *WebSocketManager) handleUnsubscription(conn *WebSocketConnection, data interface{}) {
	// 实现取消订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id":  conn.ID,
		"unsubscription": data,
	}).Debug("Handling unsubscription")
}

// handleStopGeneration 处理停止生成请求
func (wm *WebSocketManager) handleStopGeneration(conn *WebSocketConnection, msg WSMessage) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"user_id":       conn.UserID,
	}).Info("🛑 WebSocket: 收到停止生成请求")

	// 1. 取消当前正在进行的AI处理
	if wm.enhancedHandler != nil {
		// 通知增强处理器停止当前操作
		wm.logger.Info("🛑 通知增强处理器停止当前操作")
		stopped := wm.enhancedHandler.StopProcessing(conn.SessionID, conn.UserID)
		if stopped {
			wm.logger.Info("✅ 成功停止正在进行的AI处理")
		} else {
			wm.logger.Warn("⚠️ 没有找到正在进行的AI处理")
		}
	}

	// 2. 清理可能正在进行的SSH连接
	// 注意：SSH连接清理由EnhancedWebSocketHandler中的Context取消机制处理
	wm.logger.Info("🛑 SSH连接清理由Context取消机制处理")

	// 3. 发送停止确认消息
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "generation_stopped",
		Data: map[string]interface{}{
			"status":    "stopped",
			"message":   "⏹️ 操作已停止",
			"timestamp": time.Now(),
		},
		Timestamp: time.Now(),
	})

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Info("✅ WebSocket: 停止生成请求处理完成")
}

// handleChatMessage 处理聊天消息
func (wm *WebSocketManager) handleChatMessage(conn *WebSocketConnection, msg WSMessage) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message_type":  msg.Type,
		"data_type":     fmt.Sprintf("%T", msg.Data),
	}).Info("🔥 WebSocket: handleChatMessage 被调用")

	// 检查聊天服务是否可用
	if wm.chatService == nil {
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Chat service not available",
			Timestamp: time.Now(),
		})
		return
	}

	// 提取消息内容 - 支持多种格式
	var content string

	// 尝试直接转换为字符串
	if str, ok := msg.Data.(string); ok {
		content = str
	} else if msg.Data == nil {
		// 如果Data为空，尝试从消息的其他字段获取内容
		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"msg_type":      msg.Type,
			"msg_data":      msg.Data,
		}).Warn("WebSocket: Data字段为空，尝试其他方式获取消息内容")

		// 检查是否有content字段
		if msgMap, ok := msg.Data.(map[string]interface{}); ok {
			if contentVal, exists := msgMap["content"]; exists {
				if contentStr, ok := contentVal.(string); ok {
					content = contentStr
				}
			}
		}

		// 如果仍然没有内容，返回错误
		if content == "" {
			wm.logger.Error("WebSocket: ❌ 无法从消息中提取内容")
			wm.SendToConnection(conn.ID, &WSMessage{
				Type:      "error",
				Data:      "No message content found",
				Timestamp: time.Now(),
			})
			return
		}
	} else {
		// 尝试将其他类型转换为字符串
		content = fmt.Sprintf("%v", msg.Data)
		wm.logger.WithFields(logrus.Fields{
			"connection_id":     conn.ID,
			"data_type":         fmt.Sprintf("%T", msg.Data),
			"converted_content": content,
		}).Warn("WebSocket: 消息类型不是字符串，已转换")
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"content":       content,
	}).Info("WebSocket: 处理聊天消息")

	// 删除超级快速路径，让所有请求都通过DeepSeek进行智能处理

	// 发送用户消息确认
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "user_message",
		Data: map[string]interface{}{
			"content":    content,
			"created_at": time.Now(),
		},
		Timestamp: time.Now(),
	})

	// � 移除快速路径，让所有请求都通过AI处理

	// 使用智能AI服务处理消息（支持意图识别和执行）
	wm.logger.WithFields(logrus.Fields{
		"connection_id":   conn.ID,
		"session_id":      conn.SessionID,
		"message":         content,
		"ai_service_nil":  wm.aiService == nil,
		"ai_service_type": fmt.Sprintf("%T", wm.aiService),
	}).Debug("🔥 WebSocket: 准备调用AI服务处理消息") // 改为Debug级别

	if wm.aiService != nil {
		wm.logger.Debug("WebSocket: ✅ AI服务可用，调用handleWithAIService") // 改为Debug级别
		wm.handleWithAIService(conn, content)
	} else {
		wm.logger.Error("WebSocket: ❌ AI服务为空，使用降级处理")
		// 降级到基础聊天服务
		wm.handleWithChatService(conn, content)
	}
}

// handleWithAIService 使用增强AI服务处理消息（完整5步骤流程）
func (wm *WebSocketManager) handleWithAIService(conn *WebSocketConnection, content string) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"user_id":       conn.UserID,
	}).Info("WebSocket: 开始AI处理")

	// 使用更长的超时时间支持复杂操作
	ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second) // 3分钟超时
	defer cancel()

	// 检查是否有增强处理器
	if wm.enhancedHandler == nil {
		wm.logger.Error("WebSocket: ❌ 增强处理器未初始化，降级到传统AI服务")
		wm.handleWithTraditionalAIService(conn, content)
		return
	}

	// 构建增强AI服务请求
	req := &ProcessMessageRequest{
		SessionID: conn.SessionID,
		UserID:    conn.UserID,
		Message:   content,
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Debug("WebSocket: 调用AI服务")

	// 发送处理中状态
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "processing",
		Data: map[string]interface{}{
			"status":  "processing",
			"message": "🤖 AI正在智能分析您的请求...",
			"stage":   "intent_recognition",
		},
		Timestamp: time.Now(),
	})

	// 调用增强AI服务处理消息
	response, err := wm.enhancedHandler.ProcessMessage(ctx, req)
	if err != nil {
		wm.logger.WithFields(logrus.Fields{
			"connection_id":    conn.ID,
			"session_id":       conn.SessionID,
			"error":            err.Error(),
			"enhanced_handler": fmt.Sprintf("%T", wm.enhancedHandler),
		}).Error("WebSocket: ❌ 增强AI服务处理失败，尝试降级")

		// 尝试降级到传统AI服务
		wm.handleWithTraditionalAIService(conn, content)
		return
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id":   conn.ID,
		"session_id":      conn.SessionID,
		"intent":          response.Intent,
		"content_length":  len(response.Content),
		"processing_time": response.ProcessingTime,
	}).Info("WebSocket: AI处理完成")

	// 发送处理完成状态
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "processing_complete",
		Data: map[string]interface{}{
			"status":         "completed",
			"processing_time": response.ProcessingTime.String(),
			"token_count":    response.TokenCount,
		},
		Timestamp: time.Now(),
	})

	// 发送增强AI响应 - 包含完整的分析和渲染结果
	responseData := map[string]interface{}{
		"content":        response.Content,
		"intent":         response.Intent,
		"confidence":     response.Confidence,
		"created_at":     response.Timestamp,
		"token_count":    response.TokenCount,
		"processing_time": response.ProcessingTime.String(),
		"enhanced":       true, // 标识这是增强处理的结果
		"workflow_steps": []string{
			"意图识别",
			"命令生成",
			"执行操作",
			"结果分析",
			// "智能渲染", // 临时跳过以减少等待时间
		},
	}

	// 安全地添加参数，避免循环引用
	if response.Parameters != nil {
		safeParams := make(map[string]interface{})
		for k, v := range response.Parameters {
			// 只添加基本类型，避免复杂对象的循环引用
			switch val := v.(type) {
			case string, int, int64, float64, bool:
				safeParams[k] = val
			case nil:
				safeParams[k] = nil
			default:
				// 对于复杂类型，转换为字符串
				safeParams[k] = fmt.Sprintf("%v", val)
			}
		}
		responseData["parameters"] = safeParams
	}

	responseMsg := &WSMessage{
		Type:      "assistant_message",
		Data:      responseData,
		Timestamp: time.Now(),
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message_type":  responseMsg.Type,
	}).Info("WebSocket: Sending AI response to connection")

	wm.SendToConnection(conn.ID, responseMsg)

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Info("WebSocket: AI response sent successfully")
}

// handleWithTraditionalAIService 使用传统AI服务处理消息（降级处理）
func (wm *WebSocketManager) handleWithTraditionalAIService(conn *WebSocketConnection, content string) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"user_id":       conn.UserID,
		"message":       content,
	}).Debug("🔄 WebSocket: 使用传统AI服务处理消息（降级模式）") // 改为Debug级别

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// 发送降级处理状态
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "processing",
		Data: map[string]interface{}{
			"status":  "fallback_processing",
			"message": "🔄 使用传统模式处理您的请求...",
		},
		Timestamp: time.Now(),
	})

	// 构建传统AI服务请求
	req := &ProcessMessageRequest{
		SessionID: conn.SessionID,
		UserID:    conn.UserID,
		Message:   content,
	}

	// 调用传统AI服务
	if wm.aiService != nil {
		response, err := wm.aiService.ProcessMessage(ctx, req)
		if err != nil {
			wm.logger.WithFields(logrus.Fields{
				"connection_id": conn.ID,
				"session_id":    conn.SessionID,
				"error":         err.Error(),
			}).Error("WebSocket: ❌ 传统AI服务也失败，使用基础聊天服务")

			wm.handleWithChatService(conn, content)
			return
		}

		// 发送传统AI响应
		responseData := map[string]interface{}{
			"content":     response.Content,
			"intent":      response.Intent,
			"confidence":  response.Confidence,
			"created_at":  response.Timestamp,
			"token_count": response.TokenCount,
			"fallback":    true, // 标识这是降级处理的结果
		}

		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "assistant_message",
			Data:      responseData,
			Timestamp: time.Now(),
		})

		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"session_id":    conn.SessionID,
			"intent":        response.Intent,
			"confidence":    response.Confidence,
		}).Info("✅ WebSocket: 传统AI服务处理成功")
	} else {
		wm.logger.Error("WebSocket: ❌ 传统AI服务也不可用，使用基础聊天服务")
		wm.handleWithChatService(conn, content)
	}
}

// handleWithChatService 使用基础聊天服务处理消息
func (wm *WebSocketManager) handleWithChatService(conn *WebSocketConnection, content string) {
	// 基础聊天服务处理逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message":       content,
	}).Info("WebSocket: Using basic chat service")

	// 这里可以实现基础聊天逻辑
	response := "收到您的消息：" + content

	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "assistant_message",
		Data: map[string]interface{}{
			"content":    response,
			"created_at": time.Now(),
		},
		Timestamp: time.Now(),
	})
}

// SendToConnection 发送消息到指定连接
func (wm *WebSocketManager) SendToConnection(connectionID string, message *WSMessage) error {
	wm.mutex.RLock()
	conn, exists := wm.connections[connectionID]
	wm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("connection %s not found", connectionID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	select {
	case conn.Send <- data:
		return nil
	default:
		close(conn.Send)
		wm.mutex.Lock()
		delete(wm.connections, connectionID)
		wm.mutex.Unlock()
		return fmt.Errorf("connection %s send channel is full", connectionID)
	}
}

// SendToUser 发送消息到用户的所有连接
func (wm *WebSocketManager) SendToUser(userID int64, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	sent := 0
	for _, conn := range wm.connections {
		if conn.UserID == userID {
			if err := wm.SendToConnection(conn.ID, message); err != nil {
				wm.logger.WithError(err).WithField("connection_id", conn.ID).Warn("Failed to send message to connection")
			} else {
				sent++
			}
		}
	}

	if sent == 0 {
		return fmt.Errorf("no active connections found for user %d", userID)
	}

	return nil
}

// SendToSession 发送消息到指定会话
func (wm *WebSocketManager) SendToSession(sessionID string, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	for _, conn := range wm.connections {
		if conn.SessionID == sessionID {
			return wm.SendToConnection(conn.ID, message)
		}
	}

	return fmt.Errorf("no active connection found for session %s", sessionID)
}

// BroadcastToAll 广播消息到所有连接
func (wm *WebSocketManager) BroadcastToAll(message *WSMessage) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	wm.hub.broadcast <- data
	return nil
}

// GetActiveConnections 获取活跃连接数
func (wm *WebSocketManager) GetActiveConnections() int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()
	return len(wm.connections)
}

// GetConnectionsByUser 获取用户的连接数
func (wm *WebSocketManager) GetConnectionsByUser(userID int64) int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	count := 0
	for _, conn := range wm.connections {
		if conn.UserID == userID {
			count++
		}
	}
	return count
}

// SendSystemNotification 发送系统通知
func (wm *WebSocketManager) SendSystemNotification(userID int64, notification *SystemNotificationMessage) error {
	// 设置时间戳
	notification.Timestamp = time.Now()

	// 创建WebSocket消息
	message := &WSMessage{
		Type: "system_notification",
		Data: notification,
	}

	// 发送给用户的所有连接
	return wm.SendToUser(userID, message)
}

// SendCommandProgress 发送命令执行进度
func (wm *WebSocketManager) SendCommandProgress(sessionID string, progress *CommandProgressMessage) error {
	// 设置时间戳
	progress.Timestamp = time.Now()

	// 创建WebSocket消息
	message := &WSMessage{
		Type: "command_progress",
		Data: progress,
	}

	// 发送给指定会话
	return wm.SendToSession(sessionID, message)
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	TotalConnections int                    `json:"total_connections"`
	UserConnections  map[int64]int          `json:"user_connections"`
	SessionStats     map[string]interface{} `json:"session_stats"`
}

// GetConnectionStats 获取连接统计
func (wm *WebSocketManager) GetConnectionStats() *ConnectionStats {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	userConnections := make(map[int64]int)
	sessionStats := make(map[string]interface{})

	for _, conn := range wm.connections {
		userConnections[conn.UserID]++
		if conn.SessionID != "" {
			sessionStats[conn.SessionID] = map[string]interface{}{
				"user_id": conn.UserID,
			}
		}
	}

	return &ConnectionStats{
		TotalConnections: len(wm.connections),
		UserConnections:  userConnections,
		SessionStats:     sessionStats,
	}
}

// Run Hub运行
func (h *Hub) Run() {
	for {
		select {
		case conn := <-h.register:
			h.connections[conn] = true
			h.logger.WithField("connection_id", conn.ID).Debug("Connection registered")

		case conn := <-h.unregister:
			if _, ok := h.connections[conn]; ok {
				delete(h.connections, conn)
				close(conn.Send)
				h.logger.WithField("connection_id", conn.ID).Debug("Connection unregistered")
			}

		case message := <-h.broadcast:
			for conn := range h.connections {
				select {
				case conn.Send <- message:
				default:
					close(conn.Send)
					delete(h.connections, conn)
				}
			}
		}
	}
}

// 删除超级快速路径相关方法，让所有请求都通过DeepSeek进行智能处理
