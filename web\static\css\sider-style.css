/* 现代化AI助手界面 - 真正的Sider风格 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: #f8fafc;
    color: #1a202c;
    line-height: 1.5;
    overflow: hidden;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.sider-app {
    display: flex;
    height: 100vh;
    width: 100vw;
    background: #f8fafc;
}

/* 左侧边栏 */
.sider-sidebar {
    width: 240px;
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* 品牌标识 */
.sider-brand {
    display: flex;
    align-items: center;
    padding: 20px 16px;
    border-bottom: 1px solid #f1f5f9;
}

.brand-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.brand-text {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    letter-spacing: -0.3px;
}

/* 导航菜单 */
.sider-nav {
    flex: 1;
    padding: 16px 12px;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.15s ease;
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

.nav-item:hover {
    background-color: #f7fafc;
    color: #4a5568;
}

.nav-item.active {
    background: #6366f1;
    color: white;
}

.nav-item i {
    font-size: 16px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 底部用户信息 */
.sider-user {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    background: #ffffff;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.2;
}

.user-status {
    font-size: 12px;
    color: #28a745;
    line-height: 1.2;
}

/* 主内容区域 */
.sider-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    position: relative;
}

/* 视图容器 */
.chat-view,
.hosts-view,
.monitoring-view,
.alerts-view,
.reports-view,
.terminal-view,
.files-view,
.settings-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.view-header {
    padding: 24px 32px;
    border-bottom: 1px solid #e9ecef;
    background: #ffffff;
}

.view-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.view-header p {
    font-size: 14px;
    color: #6c757d;
}

.view-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
}

.placeholder-content i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.placeholder-content p {
    font-size: 16px;
}



/* 聊天区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: #f8fafc;
}

/* 欢迎界面 */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 80px 48px;
    background: linear-gradient(135deg, #fafbfc 0%, #f3f4f6 100%);
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-title {
    font-size: 48px;
    font-weight: 300;
    color: #1a1a1a;
    margin-bottom: 8px;
    letter-spacing: -0.02em;
}

.welcome-subtitle {
    font-size: 20px;
    color: #6c757d;
    margin-bottom: 48px;
    font-weight: 400;
}

/* 快捷建议 */
.quick-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.suggestion-chip {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
}

.suggestion-chip:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-chip i {
    margin-right: 8px;
    font-size: 16px;
}

/* 输入区域 */
.chat-input-container {
    padding: 20px 40px 24px;
    background: #f8fafc;
    max-width: 800px;
    margin: 0 auto;
    width: 90%;
}

/* 控制栏 */
.input-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 14px 18px;
    transition: all 0.2s ease;
    min-height: 50px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 模型选择器 */
.model-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    color: #4a5568;
}

.model-selector:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
}

.model-icon {
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
}

.model-name {
    font-weight: 600;
    color: #2d3748;
}

/* 模型下拉菜单 */
.model-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;
}

.model-dropdown-content {
    padding: 4px 0;
}

.model-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.15s ease;
    border-bottom: 1px solid #f7fafc;
}

.model-option:last-child {
    border-bottom: none;
}

.model-option:hover {
    background: #f7fafc;
}

.model-option.current {
    background: #edf2f7;
    color: #2d3748;
}

.model-option-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.model-option-info {
    flex: 1;
}

.model-option-name {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.model-option-desc {
    font-size: 11px;
    color: #718096;
    line-height: 1.3;
}

.model-option-provider {
    font-size: 10px;
    color: #a0aec0;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: auto;
}

/* 输入框 */
#chat-input {
    width: 100%;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 15px;
    line-height: 1.5;
    color: #1f2937;
    font-family: inherit;
    min-height: 40px;
    max-height: 160px;
    padding: 0;
}

#chat-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* 功能按钮 */
.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: #ffffff;
    color: #718096;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
}

.action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #4a5568;
}

/* 新建对话按钮 */
.action-btn.new-chat-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.action-btn.new-chat-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6a4190 100%);
}

/* 图标备选方案 - 如果Bootstrap Icons不加载 */
.action-btn .bi::before {
    font-family: "bootstrap-icons", sans-serif;
}

/* 聊天历史图标备选 */
.action-btn[title="聊天历史"] .bi:not([class*="bi-"])::before {
    content: "💬";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
    font-size: 10px;
}

/* 新建对话图标备选 */
.action-btn[title="新建对话"] .bi:not([class*="bi-"])::before {
    content: "➕";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
    font-size: 10px;
}

/* 消息样式 - 居中布局 */
.message {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    padding: 0;
    animation: fadeInUp 0.3s ease;
    max-width: 800px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* 消息头部（AI头像和名称） */
.message-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    align-self: flex-start;
    width: 100%;
}

.message-avatar {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.message-sender {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
}

.message-content {
    width: 100%;
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 15px;
    line-height: 1.6;
    text-align: left;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
}

.message.assistant .message-content {
    background: #f8f9fa;
    color: #1a1a1a;
    border: 1px solid #e9ecef;
    border-radius: 16px;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sider-sidebar {
        width: 240px;
    }

    .sider-main {
        max-width: calc(100vw - 240px);
    }

    .welcome-title {
        font-size: 36px;
    }

    .welcome-subtitle {
        font-size: 18px;
    }

    .quick-suggestions {
        flex-direction: column;
        align-items: center;
    }

    .suggestion-chip {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .model-selector .model-name {
        display: none;
    }

    .chat-input-container {
        width: 95%;
        padding: 16px 20px;
    }

    .input-wrapper {
        padding: 16px 18px;
        min-height: 80px;
    }
}

@media (max-width: 480px) {
    .sider-sidebar {
        width: 60px;
    }

    .brand-text,
    .nav-item span {
        display: none;
    }

    .sider-user .user-info {
        display: none;
    }

    .model-selector {
        padding: 6px 8px;
    }

    .model-selector .model-name {
        display: none;
    }

    .chat-input-container {
        width: 98%;
        padding: 12px 16px;
    }

    .input-wrapper {
        padding: 12px 16px;
        min-height: 70px;
    }
}

/* 右侧聊天历史面板 */
.chat-history-panel {
    width: 320px;
    background: #ffffff;
    border-left: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.chat-history-panel.open {
    transform: translateX(0);
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.history-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.close-history-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-history-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.history-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.history-search {
    padding: 16px 24px;
    border-bottom: 1px solid #e9ecef;
}

.history-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.history-search-input:focus {
    border-color: #667eea;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.history-item {
    padding: 12px 24px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f8f9fa;
}

.history-item:hover {
    background: #f8f9fa;
}

.history-item-title {
    font-size: 14px;
    color: #1a1a1a;
    margin-bottom: 4px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-time {
    font-size: 12px;
    color: #6c757d;
}

.no-history {
    padding: 40px 24px;
    text-align: center;
    color: #6c757d;
    font-size: 14px;
}



/* 滚动条美化 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 主机管理样式 */
.hosts-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
    margin-right: 20px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 14px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: #f8fafc;
    transition: all 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-group {
    display: flex;
    gap: 12px;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.hosts-container {
    flex: 1;
    overflow-y: auto;
}

.hosts-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #718096;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hosts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    padding: 0 20px 20px;
}

.host-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.host-card:hover {
    border-color: #cbd5e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.host-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.host-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.host-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.host-status.online {
    background: #d4edda;
    color: #155724;
}

.host-status.offline {
    background: #f8d7da;
    color: #721c24;
}

.host-status.unknown {
    background: #fff3cd;
    color: #856404;
}

.host-info {
    margin-bottom: 16px;
}

.host-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #4a5568;
}

.host-info-item i {
    width: 16px;
    margin-right: 8px;
    color: #718096;
}

.host-actions {
    display: flex;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #f1f5f9;
}

.host-action-btn {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.host-action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.host-action-btn.primary {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.host-action-btn.primary:hover {
    background: #5856eb;
}

.host-action-btn.danger {
    color: #e53e3e;
    border-color: #fed7d7;
}

.host-action-btn.danger:hover {
    background: #fed7d7;
}

.hosts-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #718096;
    text-align: center;
}

.hosts-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #cbd5e0;
}

.hosts-empty h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #4a5568;
}

.hosts-empty p {
    font-size: 14px;
    margin-bottom: 20px;
    max-width: 300px;
}

/* 添加主机模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f8fafc;
    color: #4a5568;
}

.modal-body {
    padding: 20px 24px;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input.error {
    border-color: #ef4444;
}

.form-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
}

.form-row {
    display: flex;
    gap: 12px;
}

.form-row .form-group {
    flex: 1;
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.btn-primary:hover {
    background: #5856eb;
    border-color: #5856eb;
}

.btn-secondary {
    background: white;
    color: #4a5568;
    border-color: #e2e8f0;
}

.btn-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 连接测试状态样式 */
.connection-test {
    margin-top: 12px;
    padding: 12px;
    border-radius: 6px;
    font-size: 14px;
}

.connection-test.testing {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.connection-test.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.connection-test.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .hosts-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .search-box {
        margin-right: 0;
        max-width: none;
    }

    .hosts-grid {
        grid-template-columns: 1fr;
        padding: 0 12px 12px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .form-row {
        flex-direction: column;
    }

    .modal-footer {
        flex-direction: column-reverse;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* 通知系统样式 */
.notification {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #6366f1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.notification.success {
    border-left-color: #10b981;
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.warning {
    border-left-color: #f59e0b;
}

.notification.info {
    border-left-color: #3b82f6;
}

.notification-content {
    flex: 1;
    display: flex;
    align-items: center;
}

.notification-icon {
    margin-right: 12px;
    font-size: 18px;
}

.notification.success .notification-icon {
    color: #10b981;
}

.notification.error .notification-icon {
    color: #ef4444;
}

.notification.warning .notification-icon {
    color: #f59e0b;
}

.notification.info .notification-icon {
    color: #3b82f6;
}

.notification-message {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 监控界面样式 */
.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.monitoring-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.2s ease;
}

.monitoring-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border-color: #cbd5e0;
}

.monitoring-card.full-width {
    grid-column: 1 / -1;
}

.monitoring-card.chart-card {
    min-height: 300px;
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8fafc;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header h3 i {
    color: #6366f1;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator:contains("正常"), .status-indicator:contains("良好") {
    background: #d4edda;
    color: #155724;
}

.status-indicator:contains("警告") {
    background: #fff3cd;
    color: #856404;
}

.status-indicator:contains("异常"), .status-indicator:contains("严重") {
    background: #f8d7da;
    color: #721c24;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-actions select {
    padding: 4px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.btn-small {
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-small:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.card-content {
    padding: 20px;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.progress-bar {
    height: 8px;
    background: #f1f5f9;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    transition: width 0.3s ease;
}

.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.metric-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.metric-label {
    font-size: 14px;
    color: #4a5568;
    font-weight: 500;
}

.metric-value {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #1a202c;
}

.metric-bar {
    width: 100px;
    height: 6px;
    background: #f1f5f9;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    transition: width 0.3s ease;
}

.alert-summary {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;
}

.alert-item {
    text-align: center;
    padding: 8px;
    border-radius: 6px;
}

.alert-item.critical {
    background: #fee2e2;
}

.alert-item.warning {
    background: #fef3c7;
}

.alert-item.info {
    background: #dbeafe;
}

.alert-count {
    display: block;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 4px;
}

.alert-item.critical .alert-count {
    color: #dc2626;
}

.alert-item.warning .alert-count {
    color: #d97706;
}

.alert-item.info .alert-count {
    color: #2563eb;
}

.alert-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.recent-alerts {
    max-height: 120px;
    overflow-y: auto;
}

.no-alerts {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
}

.hosts-table {
    overflow-x: auto;
}

.table-loading {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.table-loading i {
    margin-right: 8px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* 主机详情表格样式 */
.hosts-detail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.hosts-detail-table th,
.hosts-detail-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
}

.hosts-detail-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hosts-detail-table tbody tr:hover {
    background: #f8fafc;
}

.host-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.host-name-cell i {
    color: #6366f1;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.online {
    background: #d4edda;
    color: #155724;
}

.status-badge.offline {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.unknown {
    background: #fff3cd;
    color: #856404;
}

.metric-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mini-bar {
    width: 40px;
    height: 4px;
    background: #f1f5f9;
    border-radius: 2px;
    overflow: hidden;
}

.mini-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    transition: width 0.3s ease;
}

.time-cell {
    color: #6b7280;
    font-size: 12px;
}

/* 告警项目样式 */
.alert-item-small {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-size: 12px;
}

.alert-item-small.critical {
    background: #fee2e2;
    border-left: 3px solid #dc2626;
}

.alert-item-small.warning {
    background: #fef3c7;
    border-left: 3px solid #d97706;
}

.alert-item-small.info {
    background: #dbeafe;
    border-left: 3px solid #2563eb;
}

.alert-time {
    font-weight: 500;
    color: #374151;
}

.alert-message {
    color: #6b7280;
    flex: 1;
    margin-left: 8px;
}

/* 响应式监控界面 */
@media (max-width: 768px) {
    .monitoring-grid {
        grid-template-columns: 1fr;
        padding: 12px;
        gap: 16px;
    }

    .monitoring-card.chart-card {
        min-height: 250px;
    }

    .card-header {
        padding: 12px 16px;
    }

    .card-content {
        padding: 16px;
    }

    .stats-row {
        flex-direction: column;
        gap: 12px;
    }

    .performance-metrics {
        gap: 12px;
    }

    .metric-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .hosts-detail-table {
        font-size: 12px;
    }

    .hosts-detail-table th,
    .hosts-detail-table td {
        padding: 8px 12px;
    }

    .metric-cell {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .mini-bar {
        width: 60px;
    }
}

/* 监控界面动画效果 */
.monitoring-card {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数值变化动画 */
.stat-value, .metric-value span, .alert-count {
    transition: all 0.3s ease;
}

.stat-value.updated, .metric-value span.updated, .alert-count.updated {
    transform: scale(1.1);
    color: #6366f1;
}

/* 进度条动画 */
.progress-fill, .metric-fill, .mini-fill {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 状态指示器动画 */
.status-indicator {
    transition: all 0.3s ease;
    position: relative;
}

.status-indicator::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.6;
}

.status-indicator.warning::before {
    animation: pulse 2s infinite;
}

.status-indicator.error::before {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.2);
    }
}

/* 图表容器优化 */
.chart-card .card-content {
    position: relative;
    height: 200px;
}

.chart-card canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
}

/* 加载状态优化 */
.table-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* 悬停效果增强 */
.monitoring-card:hover .card-header h3 i {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.hosts-detail-table tbody tr:hover .host-name-cell i {
    color: #5856eb;
    transform: scale(1.1);
    transition: all 0.2s ease;
}

/* 刷新按钮动画 */
.refresh-btn:active, .btn-small:active {
    transform: scale(0.95);
}

.refresh-btn i, .btn-small i {
    transition: transform 0.3s ease;
}

.refresh-btn:hover i, .btn-small:hover i {
    transform: rotate(180deg);
}

/* 最后更新时间样式 */
.last-update {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    .monitoring-card {
        background: #1f2937;
        border-color: #374151;
    }

    .card-header {
        background: #111827;
        border-color: #374151;
    }

    .card-header h3 {
        color: #f9fafb;
    }

    .hosts-detail-table th {
        background: #111827;
        color: #d1d5db;
    }

    .hosts-detail-table tbody tr:hover {
        background: #111827;
    }
}

/* 告警管理样式 */
.alerts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    padding: 0 20px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.stat-card.critical {
    border-left: 4px solid #dc2626;
}

.stat-card.warning {
    border-left: 4px solid #d97706;
}

.stat-card.info {
    border-left: 4px solid #2563eb;
}

.stat-card.resolved {
    border-left: 4px solid #059669;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.stat-card.critical .stat-icon {
    background: #fee2e2;
    color: #dc2626;
}

.stat-card.warning .stat-icon {
    background: #fef3c7;
    color: #d97706;
}

.stat-card.info .stat-icon {
    background: #dbeafe;
    color: #2563eb;
}

.stat-card.resolved .stat-icon {
    background: #d1fae5;
    color: #059669;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #1a202c;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.alerts-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.alerts-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
}

.alerts-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #718096;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.alert-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.alert-item:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-item.critical {
    border-left: 4px solid #dc2626;
    background: #fef9f9;
}

.alert-item.warning {
    border-left: 4px solid #d97706;
    background: #fffbf5;
}

.alert-item.info {
    border-left: 4px solid #2563eb;
    background: #f8faff;
}

.alert-item.acknowledged {
    opacity: 0.7;
}

.alert-item.resolved {
    opacity: 0.5;
    border-left-color: #059669;
    background: #f0fdf4;
}

.alert-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.alert-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-severity {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.alert-severity.critical {
    background: #dc2626;
    color: white;
}

.alert-severity.warning {
    background: #d97706;
    color: white;
}

.alert-severity.info {
    background: #2563eb;
    color: white;
}

.alert-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #6b7280;
}

.alert-meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.alert-message {
    font-size: 14px;
    color: #374151;
    margin-bottom: 12px;
    line-height: 1.5;
}

.alert-actions {
    display: flex;
    gap: 8px;
}

.alert-action-btn {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.alert-action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.alert-action-btn.primary {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.alert-action-btn.primary:hover {
    background: #5856eb;
}

.alert-action-btn.success {
    background: #059669;
    color: white;
    border-color: #059669;
}

.alert-action-btn.success:hover {
    background: #047857;
}

.alerts-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #718096;
    text-align: center;
}

.alerts-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #059669;
}

.alerts-empty h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #4a5568;
}

.alerts-empty p {
    font-size: 14px;
    max-width: 300px;
}

/* 报表界面样式 */
.reports-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 24px;
    padding: 0 20px;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-btn:hover {
    color: #374151;
    background: #f8fafc;
}

.tab-btn.active {
    color: #6366f1;
    border-bottom-color: #6366f1;
    background: #f8faff;
}

.report-content {
    padding: 0 20px;
}

.report-section {
    margin-bottom: 32px;
}

.report-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f1f5f9;
}

.health-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.health-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.health-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border-color: #cbd5e0;
}

.health-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #6366f1;
}

.health-info {
    flex: 1;
}

.health-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.health-stats {
    display: flex;
    gap: 16px;
}

.health-stats .stat-item {
    font-size: 12px;
    color: #6b7280;
}

.health-stats .stat-item strong {
    color: #1a202c;
    font-weight: 600;
}

.trend-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.chart-container {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.chart-container h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
}

.performance-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.perf-metric {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.metric-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #6366f1;
}

.metric-chart {
    height: 150px;
    position: relative;
}

.metric-chart canvas {
    width: 100% !important;
    height: 100% !important;
}

.alerts-distribution {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
    align-items: center;
}

.distribution-chart {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    height: 300px;
    position: relative;
}

.distribution-chart canvas {
    width: 100% !important;
    height: 100% !important;
}

.distribution-stats {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.dist-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
}

.dist-label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.dist-value {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
}

.dist-percent {
    font-size: 12px;
    color: #6b7280;
}

.hosts-report-table {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.custom-report-builder {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 24px;
}

.builder-form {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.builder-form .form-group {
    margin-bottom: 16px;
}

.builder-form label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.builder-form input,
.builder-form select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
}

.metrics-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metrics-checkboxes label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin-bottom: 0;
}

.custom-report-result {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    min-height: 300px;
}

/* 报表表格样式 */
.report-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.report-table th,
.report-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
}

.report-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.report-table tbody tr:hover {
    background: #f8fafc;
}

/* 自定义报表样式 */
.custom-report-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.custom-report-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 8px;
}

.custom-report-header p {
    font-size: 14px;
    color: #6b7280;
    margin: 4px 0;
}

.custom-report-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.metric-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
}

.metric-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.metric-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-current {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
}

.metric-trend {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.metric-trend.up {
    color: #059669;
}

.metric-trend.down {
    color: #dc2626;
}

.custom-report-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
}

/* 响应式报表界面 */
@media (max-width: 768px) {
    .reports-tabs {
        flex-wrap: wrap;
        padding: 0 12px;
    }

    .tab-btn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .report-content {
        padding: 0 12px;
    }

    .health-summary {
        grid-template-columns: 1fr;
    }

    .trend-charts {
        grid-template-columns: 1fr;
    }

    .performance-overview {
        grid-template-columns: 1fr;
    }

    .alerts-distribution {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .custom-report-builder {
        grid-template-columns: 1fr;
    }

    .custom-report-content {
        grid-template-columns: 1fr;
    }

    .custom-report-actions {
        flex-direction: column;
    }

    .report-table {
        font-size: 12px;
    }

    .report-table th,
    .report-table td {
        padding: 8px 12px;
    }
}

/* 远程终端样式 */
.terminal-tabs {
    display: flex;
    align-items: center;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 20px;
    min-height: 48px;
}

.tabs-container {
    display: flex;
    flex: 1;
    gap: 4px;
}

.terminal-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 200px;
}

.terminal-tab:hover {
    background: #f8fafc;
}

.terminal-tab.active {
    background: white;
    border-color: #6366f1;
    color: #6366f1;
}

.tab-title {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.tab-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.add-tab-btn {
    background: none;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px 12px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-tab-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #374151;
}

.terminal-container {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    overflow: hidden;
}

.terminal-welcome {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8fafc;
    color: #6b7280;
}

.welcome-content {
    text-align: center;
    max-width: 400px;
}

.welcome-content i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #cbd5e0;
}

.welcome-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.welcome-content p {
    font-size: 14px;
    margin-bottom: 20px;
}

.terminal-instance {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #1a1a1a;
    color: #ffffff;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    padding: 16px;
    overflow-y: auto;
    display: none;
}

.terminal-instance.active {
    display: block;
}

.terminal-output {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-bottom: 8px;
}

.terminal-input-line {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.terminal-prompt {
    color: #10b981;
    margin-right: 8px;
    user-select: none;
}

.terminal-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: inherit;
    font-size: inherit;
    outline: none;
    caret-color: #ffffff;
}

.terminal-cursor {
    display: inline-block;
    width: 8px;
    height: 16px;
    background: #ffffff;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.terminal-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 20px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-btn {
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.tool-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.tool-btn.danger {
    color: #e53e3e;
    border-color: #fed7d7;
}

.tool-btn.danger:hover {
    background: #fed7d7;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
}

.connection-status i {
    font-size: 8px;
}

.connection-status.connected {
    color: #059669;
}

.connection-status.connecting {
    color: #d97706;
}

.connection-status.disconnected {
    color: #dc2626;
}

/* 新建终端模态框样式 */
.new-terminal-modal .modal-content {
    max-width: 600px;
}

.terminal-form {
    display: grid;
    gap: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.quick-connect {
    margin-bottom: 16px;
}

.quick-connect h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.host-quick-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.host-quick-item {
    padding: 6px 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.host-quick-item:hover {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.terminal-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 16px;
}

.option-group {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
}

.option-group h5 {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 12px;
}

.option-item:last-child {
    margin-bottom: 0;
}

/* 文件管理样式 */
.files-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 16px;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.host-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.host-selector label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

.host-selector select {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    min-width: 150px;
}

.path-navigator {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.path-breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    overflow-x: auto;
    white-space: nowrap;
}

.path-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #4a5568;
    transition: all 0.2s ease;
}

.path-item:hover {
    background: #e2e8f0;
    color: #1a202c;
}

.path-item.root {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.path-item.root:hover {
    background: #5856eb;
}

.path-separator {
    color: #9ca3af;
    margin: 0 4px;
}

.path-input-group {
    display: flex;
    align-items: center;
    gap: 4px;
}

.path-input-group input {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

.path-input-group button {
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.path-input-group button:hover {
    background: #f8fafc;
}

.view-toggle {
    display: flex;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.view-btn {
    padding: 6px 8px;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    border-right: 1px solid #e2e8f0;
}

.view-btn:last-child {
    border-right: none;
}

.view-btn:hover {
    background: #f8fafc;
}

.view-btn.active {
    background: #6366f1;
    color: white;
}

.files-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

.files-connect-prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6b7280;
}

.connect-content {
    text-align: center;
    max-width: 400px;
}

.connect-content i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #cbd5e0;
}

.connect-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.connect-content p {
    font-size: 14px;
}

.files-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #718096;
}

.files-list {
    padding: 20px;
}

.files-list.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
}

.files-list.list-view {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.file-item:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.file-item.selected {
    border-color: #6366f1;
    background: #f8faff;
}

.files-list.grid-view .file-item {
    flex-direction: column;
    text-align: center;
    aspect-ratio: 1;
    justify-content: center;
    gap: 8px;
}

.files-list.list-view .file-item {
    gap: 12px;
}

.file-icon {
    font-size: 24px;
    color: #6b7280;
}

.file-icon.folder {
    color: #f59e0b;
}

.file-icon.image {
    color: #10b981;
}

.file-icon.document {
    color: #3b82f6;
}

.file-icon.archive {
    color: #8b5cf6;
}

.file-icon.executable {
    color: #ef4444;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.files-list.grid-view .file-info {
    flex: none;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #1a202c;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.files-list.grid-view .file-name {
    white-space: normal;
    word-break: break-word;
    line-height: 1.3;
}

.file-meta {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
}

.files-list.grid-view .file-meta {
    display: none;
}

.files-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #718096;
    text-align: center;
}

.files-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #cbd5e0;
}

.files-empty h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #4a5568;
}

.files-empty p {
    font-size: 14px;
}

.file-operations {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 280px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.operations-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 8px 8px 0 0;
}

.operations-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.close-operations {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-operations:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.operations-content {
    padding: 20px;
}

.selected-file-info {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
}

.operations-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.op-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.op-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.op-btn.danger {
    color: #e53e3e;
    border-color: #fed7d7;
}

.op-btn.danger:hover {
    background: #fed7d7;
}

/* 系统设置样式 */
.settings-nav {
    display: flex;
    flex-direction: column;
    width: 240px;
    background: white;
    border-right: 1px solid #e2e8f0;
    padding: 20px 0;
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    border: none;
    background: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.settings-nav-item:hover {
    background: #f8fafc;
    color: #374151;
}

.settings-nav-item.active {
    background: #f8faff;
    color: #6366f1;
    border-right: 2px solid #6366f1;
}

.settings-nav-item i {
    font-size: 16px;
}

.settings-content {
    flex: 1;
    padding: 20px 40px;
    overflow-y: auto;
}

.settings-panel {
    max-width: 800px;
}

.settings-panel h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.settings-section {
    margin-bottom: 32px;
}

.settings-section h4 {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
}

.setting-item {
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.setting-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.setting-label span {
    flex: 1;
}

.setting-input,
.setting-select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    min-width: 200px;
}

.setting-input:focus,
.setting-select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.checkbox-label {
    flex-direction: row;
    align-items: center;
    gap: 12px;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #6366f1;
}

.setting-description {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

.color-picker {
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-picker input[type="color"] {
    width: 40px;
    height: 40px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
}

.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #6366f1;
    border: 1px solid #e2e8f0;
}

.about-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.about-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.about-item:last-child {
    border-bottom: none;
}

.about-label {
    font-weight: 500;
    color: #374151;
}

.about-value {
    color: #6b7280;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.tech-stack {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.tech-item i {
    font-size: 24px;
    color: #6366f1;
    width: 32px;
    text-align: center;
}

.tech-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 4px;
}

.tech-desc {
    font-size: 14px;
    color: #6b7280;
}

.system-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.action-btn.danger {
    color: #e53e3e;
    border-color: #fed7d7;
}

.action-btn.danger:hover {
    background: #fed7d7;
}

/* 性能统计样式 */
.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.stat-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #1a202c;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.performance-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 加载状态优化 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #6b7280;
    text-align: center;
}

.error-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ef4444;
}

.error-state h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.error-state p {
    font-size: 14px;
    margin-bottom: 20px;
}

.error-state .retry-btn {
    padding: 8px 16px;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.error-state .retry-btn:hover {
    background: #5856eb;
}

/* 性能优化相关样式 */
.optimized-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* 缓存状态指示器 */
.cache-indicator {
    position: relative;
}

.cache-indicator::after {
    content: '⚡';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    color: #10b981;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.cache-indicator.cached::after {
    opacity: 1;
}

/* 性能警告 */
.performance-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fef3cd;
    border: 1px solid #fbbf24;
    border-radius: 6px;
    color: #92400e;
    font-size: 14px;
    margin-bottom: 16px;
}

.performance-warning i {
    color: #f59e0b;
}

/* 响应式性能优化 */
@media (max-width: 768px) {
    .performance-stats {
        grid-template-columns: 1fr;
    }

    .performance-actions {
        flex-direction: column;
    }

    .stat-item {
        padding: 8px 12px;
    }
}

/* 用户体验优化 - 高级动画效果 */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.slide-in-right {
    animation: slideInRight 0.4s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.4s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.4s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.4s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
    }
}

/* 悬停效果增强 */
.enhanced-hover {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-hover:hover::before {
    left: 100%;
}

.enhanced-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 按钮增强效果 */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-enhanced::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn-enhanced:active::after {
    width: 300px;
    height: 300px;
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 输入框焦点效果 */
.input-enhanced {
    position: relative;
    transition: all 0.3s ease;
}

.input-enhanced:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 加载状态增强 */
.loading-enhanced {
    position: relative;
    overflow: hidden;
}

.loading-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 通知增强效果 */
.notification-enhanced {
    animation: notificationSlide 0.5s ease-out;
    backdrop-filter: blur(10px);
    border-left: 4px solid;
}

.notification-enhanced.success {
    border-left-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.notification-enhanced.error {
    border-left-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.notification-enhanced.warning {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.notification-enhanced.info {
    border-left-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

@keyframes notificationSlide {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* 进度条增强 */
.progress-enhanced {
    position: relative;
    background: #f3f4f6;
    border-radius: 10px;
    overflow: hidden;
    height: 8px;
}

.progress-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
    border-radius: 10px;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
    from {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
    to {
        box-shadow: 0 0 15px rgba(99, 102, 241, 0.8);
    }
}

/* 智能化功能样式 */
.batch-operations-bar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    gap: 24px;
    z-index: 1000;
    backdrop-filter: blur(10px);
    animation: slideInUp 0.3s ease-out;
}

.batch-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.selected-count {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.clear-selection {
    background: none;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 4px 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.clear-selection:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
}

.batch-actions {
    display: flex;
    gap: 8px;
}

.batch-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.batch-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.batch-btn.danger {
    color: #e53e3e;
    border-color: #fed7d7;
}

.batch-btn.danger:hover {
    background: #fed7d7;
}

/* 智能告警样式 */
.smart-alert {
    position: relative;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 4px solid;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    animation: slideInRight 0.3s ease-out;
}

.smart-alert.warning {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.smart-alert.critical {
    border-left-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.smart-alert.info {
    border-left-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.alert-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.alert-title {
    font-size: 14px;
    font-weight: 600;
    color: #1a202c;
}

.alert-time {
    font-size: 12px;
    color: #6b7280;
}

.alert-message {
    font-size: 13px;
    color: #4a5568;
    line-height: 1.4;
}

/* 预测分析样式 */
.prediction-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.prediction-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.prediction-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;
}

.prediction-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.prediction-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.prediction-content {
    position: relative;
    z-index: 1;
}

.prediction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.prediction-item:last-child {
    border-bottom: none;
}

.prediction-label {
    font-size: 14px;
    opacity: 0.9;
}

.prediction-value {
    font-size: 16px;
    font-weight: 600;
}

/* 自动化状态指示器 */
.automation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 12px;
}

.automation-status.active {
    background: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
    color: #065f46;
}

.automation-status.inactive {
    background: rgba(156, 163, 175, 0.1);
    border-color: #9ca3af;
    color: #374151;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

/* 容量规划建议 */
.capacity-recommendations {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.recommendation-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
}

.recommendation-icon.high {
    background: #ef4444;
}

.recommendation-icon.medium {
    background: #f59e0b;
}

.recommendation-icon.low {
    background: #10b981;
}

.recommendation-content {
    flex: 1;
}

.recommendation-type {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.recommendation-message {
    font-size: 14px;
    color: #374151;
    line-height: 1.4;
}

/* 异常检测样式 */
.anomaly-detection {
    background: #fef3cd;
    border: 1px solid #fbbf24;
    border-radius: 8px;
    padding: 12px 16px;
    margin: 12px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.anomaly-icon {
    color: #f59e0b;
    font-size: 18px;
}

.anomaly-content {
    flex: 1;
}

.anomaly-title {
    font-size: 14px;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 2px;
}

.anomaly-description {
    font-size: 12px;
    color: #b45309;
}

/* 响应式智能化功能 */
@media (max-width: 768px) {
    .batch-operations-bar {
        left: 10px;
        right: 10px;
        transform: none;
        flex-direction: column;
        gap: 12px;
    }

    .batch-actions {
        width: 100%;
        justify-content: space-between;
    }

    .batch-btn {
        flex: 1;
        justify-content: center;
    }

    .prediction-panel {
        padding: 16px;
    }

    .prediction-header {
        flex-direction: column;
        text-align: center;
    }
}

/* 响应式设置界面 */
@media (max-width: 768px) {
    .settings-view .view-content {
        flex-direction: column;
    }

    .settings-nav {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
        padding: 12px 0;
    }

    .settings-nav-item {
        white-space: nowrap;
        padding: 8px 16px;
        border-right: none;
        border-bottom: 2px solid transparent;
    }

    .settings-nav-item.active {
        border-right: none;
        border-bottom-color: #6366f1;
    }

    .settings-content {
        padding: 20px 16px;
    }

    .setting-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .setting-input,
    .setting-select {
        width: 100%;
        min-width: auto;
    }

    .system-actions {
        grid-template-columns: 1fr;
    }
}
