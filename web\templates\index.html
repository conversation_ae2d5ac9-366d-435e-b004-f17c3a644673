<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维助手 - 智能对话管理平台</title>

    <!-- 🔧 添加缓存破坏机制，强制刷新JavaScript -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- 现代化Sider风格样式 -->
    <link href="/static/css/sider-style.css" rel="stylesheet">

    <!-- 图标 -->
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 🎨 聊天历史面板优化样式 -->
    <style>
        /* 聊天历史面板基础样式 */
        .chat-history-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.2);
        }

        .chat-history-panel.open {
            right: 0;
        }

        /* 历史面板头部 */
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .history-header h3 {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .close-history-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .close-history-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        /* 历史面板内容 */
        .history-content {
            height: calc(100vh - 80px);
            overflow-y: auto;
            padding: 16px;
        }

        /* 搜索框样式 */
        .history-search {
            margin-bottom: 16px;
        }

        .history-search-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .history-search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .history-search-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
        }

        /* 历史项目样式 */
        .history-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .history-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .history-item-content {
            flex: 1;
            padding: 16px;
            cursor: pointer;
        }

        .history-item-actions {
            padding: 8px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .history-item:hover .history-item-actions {
            opacity: 1;
        }

        .history-delete-btn {
            background: rgba(255, 0, 0, 0.2);
            border: none;
            color: rgba(255, 255, 255, 0.8);
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .history-delete-btn:hover {
            background: rgba(255, 0, 0, 0.4);
            color: white;
            transform: scale(1.1);
        }

        .history-item-title {
            color: white;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .history-item-time {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            display: flex;
            align-items: center;
        }

        .history-item-time::before {
            content: '🕒';
            margin-right: 6px;
        }

        /* 无历史记录样式 */
        .no-history {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            padding: 40px 20px;
        }

        .no-history::before {
            content: '📝';
            display: block;
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 滚动条样式 */
        .history-content::-webkit-scrollbar {
            width: 6px;
        }

        .history-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .history-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .history-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 🎨 加载动画和用户体验优化 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
        }

        /* 消息发送状态指示器 */
        .message-status {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .status-sending {
            color: #ffa500;
        }

        .status-sent {
            color: #4caf50;
        }

        .status-error {
            color: #f44336;
        }

        /* 错误提示优化 */
        .error-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 400px;
            word-wrap: break-word;
        }

        .error-toast.show {
            transform: translateX(0);
        }

        .success-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 400px;
            word-wrap: break-word;
        }

        .success-toast.show {
            transform: translateX(0);
        }

        .toast-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 18px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .toast-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 输入框增强 */
        .chat-input:focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
            border-color: #667eea;
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 消息动画 */
        .message {
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 批量操作样式 */
        .batch-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .batch-count {
            font-size: 14px;
            font-weight: 500;
            color: #667eea;
            margin-right: 8px;
        }

        .host-select {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 10;
        }

        .host-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .host-card {
            position: relative;
        }

        .host-card-header {
            position: relative;
            padding-left: 32px;
        }

        /* 打字指示器 */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            margin: 10px 0;
            max-width: 80px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: typingBounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typingBounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Sider风格的现代化应用容器 -->
    <div class="sider-app">
        <!-- 左侧边栏 -->
        <aside class="sider-sidebar">
            <!-- 品牌标识 -->
            <div class="sider-brand">
                <div class="brand-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <span class="brand-text">AI运维助手</span>
            </div>

            <!-- 导航菜单 -->
            <nav class="sider-nav">
                <div class="nav-item active" onclick="switchToChat()">
                    <i class="bi bi-chat-dots"></i>
                    <span>聊天</span>
                </div>
                <div class="nav-item" onclick="switchToHosts()">
                    <i class="bi bi-server"></i>
                    <span>主机</span>
                </div>
                <div class="nav-item" onclick="switchToMonitoring()">
                    <i class="bi bi-activity"></i>
                    <span>监控</span>
                </div>
                <div class="nav-item" onclick="switchToAlerts()">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>告警</span>
                </div>
                <div class="nav-item" onclick="switchToReports()">
                    <i class="bi bi-graph-up"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item" onclick="switchToTerminal()">
                    <i class="bi bi-terminal"></i>
                    <span>终端</span>
                </div>
                <div class="nav-item" onclick="switchToFiles()">
                    <i class="bi bi-folder"></i>
                    <span>文件</span>
                </div>
                <div class="nav-item" onclick="switchToSettings()">
                    <i class="bi bi-gear"></i>
                    <span>设置</span>
                </div>
            </nav>

            <!-- 底部用户信息 -->
            <div class="sider-user">
                <div class="user-avatar">A</div>
                <div class="user-info">
                    <div class="user-name">管理员</div>
                    <div class="user-status">在线</div>
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="sider-main">
            <!-- 聊天界面 -->
            <div class="chat-view active" id="chat-view">
                <!-- 对话消息区域 -->
                <div class="chat-messages" id="chat-messages">
                    <!-- 欢迎界面 -->
                    <div class="welcome-screen" id="welcome-screen">
                        <div class="welcome-content">
                            <h1 class="welcome-title">你好，</h1>
                            <p class="welcome-subtitle">我今天能帮你什么？</p>

                            <!-- 快捷建议 -->
                            <div class="quick-suggestions">
                                <button class="suggestion-chip" onclick="insertSuggestion('查看主机状态')">
                                    <i class="bi bi-server"></i>
                                    查看主机状态
                                </button>
                                <button class="suggestion-chip" onclick="insertSuggestion('检查系统告警')">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    检查系统告警
                                </button>
                                <button class="suggestion-chip" onclick="insertSuggestion('生成运维报表')">
                                    <i class="bi bi-graph-up"></i>
                                    生成运维报表
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部输入区域 -->
                <div class="chat-input-container">
                    <!-- 顶部控制栏：模型选择器和功能按钮 -->
                    <div class="input-controls">
                        <!-- 左侧模型选择器 -->
                        <div class="model-selector" onclick="toggleModelSelector()">
                            <div class="model-icon">
                                <span id="current-model-icon">🤖</span>
                            </div>
                            <span class="model-name" id="current-model-name">加载中...</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>

                        <!-- 模型选择下拉菜单 -->
                        <div class="model-dropdown" id="model-dropdown" style="display: none;">
                            <div class="model-dropdown-content" id="model-dropdown-content">
                                <!-- 动态加载模型列表 -->
                            </div>
                        </div>

                        <!-- 右侧功能按钮 -->
                        <div class="input-actions">
                            <button class="action-btn" onclick="toggleHistoryPanel()" title="聊天历史">
                                <i class="bi bi-chat-dots"></i>
                            </button>
                            <button class="action-btn new-chat-btn" onclick="startNewChat()" title="新建对话">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 输入框区域 -->
                    <div class="input-wrapper">
                        <!-- 输入框 -->
                        <textarea
                            id="chat-input"
                            placeholder="向我询问任何问题..."
                            rows="3"
                            onkeydown="handleInputKeydown(event)"
                            oninput="adjustTextareaHeight(this)"
                        ></textarea>

                        <!-- 发送按钮 -->
                        <button id="send-button" class="send-button" onclick="sendMessage()" title="发送消息 (Enter)">
                            <i class="bi bi-send-fill" id="send-icon"></i>
                            <div class="loading-spinner" id="loading-spinner" style="display: none;">
                                <div class="spinner"></div>
                            </div>
                        </button>

                        <!-- 停止按钮 -->
                        <button id="stop-button" class="stop-button" onclick="stopGeneration()" title="停止生成" style="display: none;">
                            <i class="bi bi-stop-fill" id="stop-icon"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 主机管理视图 -->
            <div class="hosts-view" id="hosts-view" style="display: none;">
                <div class="view-header">
                    <h2>🖥️ 主机管理</h2>
                    <p>管理和监控您的服务器</p>
                    <div class="header-actions">
                        <button class="add-host-btn" onclick="showAddHostModal()">
                            <i class="bi bi-plus-circle"></i> 添加主机
                        </button>
                        <button class="refresh-btn" onclick="refreshHostList()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <!-- 批量操作按钮组 -->
                        <div class="batch-actions" id="batch-actions" style="display: none;">
                            <span class="batch-count" id="batch-count">已选择 0 项</span>
                            <button class="btn btn-sm btn-outline-primary" onclick="batchTestConnection()">
                                <i class="bi bi-wifi"></i> 批量测试
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="batchDeleteHosts()">
                                <i class="bi bi-trash"></i> 批量删除
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                                <i class="bi bi-x"></i> 取消选择
                            </button>
                        </div>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 搜索和过滤栏 -->
                    <div class="hosts-toolbar">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" id="host-search" placeholder="搜索主机名、IP地址..." onkeyup="filterHosts()">
                        </div>
                        <div class="filter-group">
                            <select id="status-filter" onchange="filterHosts()">
                                <option value="">所有状态</option>
                                <option value="online">在线</option>
                                <option value="offline">离线</option>
                                <option value="unknown">未知</option>
                            </select>
                            <select id="environment-filter" onchange="filterHosts()">
                                <option value="">所有环境</option>
                                <option value="production">生产环境</option>
                                <option value="staging">测试环境</option>
                                <option value="development">开发环境</option>
                            </select>
                        </div>
                    </div>

                    <!-- 主机列表 -->
                    <div class="hosts-container">
                        <div class="hosts-loading" id="hosts-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>正在加载主机列表...</p>
                        </div>

                        <div class="hosts-grid" id="hosts-grid">
                            <!-- 主机卡片将通过JavaScript动态生成 -->
                        </div>

                        <div class="hosts-empty" id="hosts-empty" style="display: none;">
                            <i class="bi bi-server"></i>
                            <h3>暂无主机</h3>
                            <p>点击"添加主机"按钮开始添加您的第一台服务器</p>
                            <button class="add-host-btn" onclick="showAddHostModal()">
                                <i class="bi bi-plus-circle"></i> 添加主机
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="monitoring-view" id="monitoring-view" style="display: none;">
                <div class="view-header">
                    <h2>🎯 实时监控仪表板</h2>
                    <p>实时监控系统性能和状态</p>
                    <div class="header-actions">
                        <button class="refresh-btn" onclick="refreshMonitoringData()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <span class="last-update" id="last-update">最后更新: --</span>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 系统概览卡片 -->
                    <div class="monitoring-grid">
                        <!-- 主机状态概览 -->
                        <div class="monitoring-card">
                            <div class="card-header">
                                <h3><i class="bi bi-server"></i> 主机状态</h3>
                                <span class="status-indicator" id="hosts-status">正常</span>
                            </div>
                            <div class="card-content">
                                <div class="stats-row">
                                    <div class="stat-item">
                                        <span class="stat-value" id="hosts-online">0</span>
                                        <span class="stat-label">在线</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value" id="hosts-offline">0</span>
                                        <span class="stat-label">离线</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value" id="hosts-total">0</span>
                                        <span class="stat-label">总计</span>
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="hosts-progress" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统性能 -->
                        <div class="monitoring-card">
                            <div class="card-header">
                                <h3><i class="bi bi-cpu"></i> 系统性能</h3>
                                <span class="status-indicator" id="performance-status">良好</span>
                            </div>
                            <div class="card-content">
                                <div class="performance-metrics">
                                    <div class="metric-item">
                                        <div class="metric-label">CPU使用率</div>
                                        <div class="metric-value">
                                            <span id="cpu-usage">--</span>%
                                            <div class="metric-bar">
                                                <div class="metric-fill" id="cpu-bar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-label">内存使用率</div>
                                        <div class="metric-value">
                                            <span id="memory-usage">--</span>%
                                            <div class="metric-bar">
                                                <div class="metric-fill" id="memory-bar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-label">磁盘使用率</div>
                                        <div class="metric-value">
                                            <span id="disk-usage">--</span>%
                                            <div class="metric-bar">
                                                <div class="metric-fill" id="disk-bar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 告警信息 -->
                        <div class="monitoring-card">
                            <div class="card-header">
                                <h3><i class="bi bi-exclamation-triangle"></i> 告警信息</h3>
                                <span class="status-indicator" id="alerts-status">正常</span>
                            </div>
                            <div class="card-content">
                                <div class="alert-summary">
                                    <div class="alert-item critical">
                                        <span class="alert-count" id="critical-alerts">0</span>
                                        <span class="alert-label">严重</span>
                                    </div>
                                    <div class="alert-item warning">
                                        <span class="alert-count" id="warning-alerts">0</span>
                                        <span class="alert-label">警告</span>
                                    </div>
                                    <div class="alert-item info">
                                        <span class="alert-count" id="info-alerts">0</span>
                                        <span class="alert-label">信息</span>
                                    </div>
                                </div>
                                <div class="recent-alerts" id="recent-alerts">
                                    <div class="no-alerts">暂无告警</div>
                                </div>
                            </div>
                        </div>

                        <!-- 性能趋势图表 -->
                        <div class="monitoring-card chart-card">
                            <div class="card-header">
                                <h3><i class="bi bi-graph-up"></i> CPU使用率趋势</h3>
                                <div class="card-actions">
                                    <select id="cpu-chart-period" onchange="updateChartPeriod('cpu', this.value)">
                                        <option value="1h">最近1小时</option>
                                        <option value="6h">最近6小时</option>
                                        <option value="24h" selected>最近24小时</option>
                                        <option value="7d">最近7天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <canvas id="cpu-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="monitoring-card chart-card">
                            <div class="card-header">
                                <h3><i class="bi bi-memory"></i> 内存使用率趋势</h3>
                                <div class="card-actions">
                                    <select id="memory-chart-period" onchange="updateChartPeriod('memory', this.value)">
                                        <option value="1h">最近1小时</option>
                                        <option value="6h">最近6小时</option>
                                        <option value="24h" selected>最近24小时</option>
                                        <option value="7d">最近7天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <canvas id="memory-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="monitoring-card chart-card">
                            <div class="card-header">
                                <h3><i class="bi bi-hdd"></i> 磁盘使用率趋势</h3>
                                <div class="card-actions">
                                    <select id="disk-chart-period" onchange="updateChartPeriod('disk', this.value)">
                                        <option value="1h">最近1小时</option>
                                        <option value="6h">最近6小时</option>
                                        <option value="24h" selected>最近24小时</option>
                                        <option value="7d">最近7天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <canvas id="disk-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- 主机列表 -->
                        <div class="monitoring-card full-width">
                            <div class="card-header">
                                <h3><i class="bi bi-list"></i> 主机详情</h3>
                                <div class="card-actions">
                                    <button class="btn-small" onclick="refreshHostDetails()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="hosts-table" id="hosts-table">
                                    <div class="table-loading">
                                        <i class="bi bi-arrow-clockwise spin"></i> 加载中...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 告警管理视图 -->
            <div class="alerts-view" id="alerts-view" style="display: none;">
                <div class="view-header">
                    <h2>⚠️ 告警管理</h2>
                    <p>查看和处理系统告警</p>
                    <div class="header-actions">
                        <button class="add-alert-rule-btn" onclick="showAddAlertRuleModal()">
                            <i class="bi bi-plus-circle"></i> 添加告警规则
                        </button>
                        <button class="refresh-btn" onclick="refreshAlerts()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 告警统计卡片 -->
                    <div class="alerts-stats">
                        <div class="stat-card critical">
                            <div class="stat-icon">
                                <i class="bi bi-exclamation-triangle-fill"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="critical-alerts-count">0</div>
                                <div class="stat-label">严重告警</div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="bi bi-exclamation-circle-fill"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="warning-alerts-count">0</div>
                                <div class="stat-label">警告告警</div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="info-alerts-count">0</div>
                                <div class="stat-label">信息告警</div>
                            </div>
                        </div>
                        <div class="stat-card resolved">
                            <div class="stat-icon">
                                <i class="bi bi-check-circle-fill"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="resolved-alerts-count">0</div>
                                <div class="stat-label">已解决</div>
                            </div>
                        </div>
                    </div>

                    <!-- 告警过滤和搜索 -->
                    <div class="alerts-toolbar">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" id="alert-search" placeholder="搜索告警消息..." onkeyup="filterAlerts()">
                        </div>
                        <div class="filter-group">
                            <select id="severity-filter" onchange="filterAlerts()">
                                <option value="">所有级别</option>
                                <option value="critical">严重</option>
                                <option value="warning">警告</option>
                                <option value="info">信息</option>
                            </select>
                            <select id="status-filter" onchange="filterAlerts()">
                                <option value="">所有状态</option>
                                <option value="active">活跃</option>
                                <option value="acknowledged">已确认</option>
                                <option value="resolved">已解决</option>
                            </select>
                            <select id="host-filter" onchange="filterAlerts()">
                                <option value="">所有主机</option>
                                <!-- 主机选项将通过JavaScript动态生成 -->
                            </select>
                        </div>
                    </div>

                    <!-- 告警列表 -->
                    <div class="alerts-container">
                        <div class="alerts-loading" id="alerts-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>正在加载告警列表...</p>
                        </div>

                        <div class="alerts-list" id="alerts-list">
                            <!-- 告警项目将通过JavaScript动态生成 -->
                        </div>

                        <div class="alerts-empty" id="alerts-empty" style="display: none;">
                            <i class="bi bi-check-circle"></i>
                            <h3>暂无告警</h3>
                            <p>系统运行正常，当前没有活跃的告警</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计报表视图 -->
            <div class="reports-view" id="reports-view" style="display: none;">
                <div class="view-header">
                    <h2>📊 统计报表</h2>
                    <p>生成和查看运维统计报表</p>
                    <div class="header-actions">
                        <button class="generate-report-btn" onclick="showGenerateReportModal()">
                            <i class="bi bi-plus-circle"></i> 生成报表
                        </button>
                        <button class="refresh-btn" onclick="refreshReports()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 报表类型选择 -->
                    <div class="reports-tabs">
                        <button class="tab-btn active" onclick="switchReportTab('overview')" data-tab="overview">
                            <i class="bi bi-pie-chart"></i> 系统概览
                        </button>
                        <button class="tab-btn" onclick="switchReportTab('performance')" data-tab="performance">
                            <i class="bi bi-speedometer2"></i> 性能分析
                        </button>
                        <button class="tab-btn" onclick="switchReportTab('alerts')" data-tab="alerts">
                            <i class="bi bi-exclamation-triangle"></i> 告警统计
                        </button>
                        <button class="tab-btn" onclick="switchReportTab('hosts')" data-tab="hosts">
                            <i class="bi bi-server"></i> 主机报告
                        </button>
                        <button class="tab-btn" onclick="switchReportTab('custom')" data-tab="custom">
                            <i class="bi bi-gear"></i> 自定义报表
                        </button>
                    </div>

                    <!-- 系统概览报表 -->
                    <div class="report-content" id="overview-report" style="display: block;">
                        <div class="report-section">
                            <h3>系统健康状况</h3>
                            <div class="health-summary">
                                <div class="health-card">
                                    <div class="health-icon">
                                        <i class="bi bi-server"></i>
                                    </div>
                                    <div class="health-info">
                                        <div class="health-title">主机状态</div>
                                        <div class="health-stats">
                                            <span class="stat-item">在线: <strong id="report-hosts-online">0</strong></span>
                                            <span class="stat-item">离线: <strong id="report-hosts-offline">0</strong></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="health-card">
                                    <div class="health-icon">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </div>
                                    <div class="health-info">
                                        <div class="health-title">告警状态</div>
                                        <div class="health-stats">
                                            <span class="stat-item">严重: <strong id="report-critical-alerts">0</strong></span>
                                            <span class="stat-item">警告: <strong id="report-warning-alerts">0</strong></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="health-card">
                                    <div class="health-icon">
                                        <i class="bi bi-cpu"></i>
                                    </div>
                                    <div class="health-info">
                                        <div class="health-title">系统负载</div>
                                        <div class="health-stats">
                                            <span class="stat-item">CPU: <strong id="report-avg-cpu">0%</strong></span>
                                            <span class="stat-item">内存: <strong id="report-avg-memory">0%</strong></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="report-section">
                            <h3>趋势分析</h3>
                            <div class="trend-charts">
                                <div class="chart-container">
                                    <h4>主机状态趋势</h4>
                                    <canvas id="hosts-trend-chart" width="400" height="200"></canvas>
                                </div>
                                <div class="chart-container">
                                    <h4>告警趋势</h4>
                                    <canvas id="alerts-trend-chart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能分析报表 -->
                    <div class="report-content" id="performance-report" style="display: none;">
                        <div class="report-section">
                            <h3>性能指标概览</h3>
                            <div class="performance-overview">
                                <div class="perf-metric">
                                    <div class="metric-header">
                                        <h4>CPU使用率</h4>
                                        <span class="metric-value" id="perf-cpu-avg">0%</span>
                                    </div>
                                    <div class="metric-chart">
                                        <canvas id="perf-cpu-chart" width="300" height="150"></canvas>
                                    </div>
                                </div>
                                <div class="perf-metric">
                                    <div class="metric-header">
                                        <h4>内存使用率</h4>
                                        <span class="metric-value" id="perf-memory-avg">0%</span>
                                    </div>
                                    <div class="metric-chart">
                                        <canvas id="perf-memory-chart" width="300" height="150"></canvas>
                                    </div>
                                </div>
                                <div class="perf-metric">
                                    <div class="metric-header">
                                        <h4>磁盘使用率</h4>
                                        <span class="metric-value" id="perf-disk-avg">0%</span>
                                    </div>
                                    <div class="metric-chart">
                                        <canvas id="perf-disk-chart" width="300" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 告警统计报表 -->
                    <div class="report-content" id="alerts-report" style="display: none;">
                        <div class="report-section">
                            <h3>告警分布统计</h3>
                            <div class="alerts-distribution">
                                <div class="distribution-chart">
                                    <canvas id="alerts-severity-chart" width="400" height="300"></canvas>
                                </div>
                                <div class="distribution-stats">
                                    <div class="dist-item">
                                        <span class="dist-label">严重告警</span>
                                        <span class="dist-value" id="dist-critical">0</span>
                                        <span class="dist-percent" id="dist-critical-percent">0%</span>
                                    </div>
                                    <div class="dist-item">
                                        <span class="dist-label">警告告警</span>
                                        <span class="dist-value" id="dist-warning">0</span>
                                        <span class="dist-percent" id="dist-warning-percent">0%</span>
                                    </div>
                                    <div class="dist-item">
                                        <span class="dist-label">信息告警</span>
                                        <span class="dist-value" id="dist-info">0</span>
                                        <span class="dist-percent" id="dist-info-percent">0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 主机报告 -->
                    <div class="report-content" id="hosts-report" style="display: none;">
                        <div class="report-section">
                            <h3>主机详细报告</h3>
                            <div class="hosts-report-table" id="hosts-report-table">
                                <div class="table-loading">
                                    <i class="bi bi-arrow-clockwise spin"></i> 生成报告中...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 自定义报表 -->
                    <div class="report-content" id="custom-report" style="display: none;">
                        <div class="report-section">
                            <h3>自定义报表生成器</h3>
                            <div class="custom-report-builder">
                                <div class="builder-form">
                                    <div class="form-group">
                                        <label>报表名称</label>
                                        <input type="text" id="custom-report-name" placeholder="输入报表名称">
                                    </div>
                                    <div class="form-group">
                                        <label>时间范围</label>
                                        <select id="custom-time-range">
                                            <option value="1h">最近1小时</option>
                                            <option value="24h">最近24小时</option>
                                            <option value="7d">最近7天</option>
                                            <option value="30d">最近30天</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>包含指标</label>
                                        <div class="metrics-checkboxes">
                                            <label><input type="checkbox" value="cpu" checked> CPU使用率</label>
                                            <label><input type="checkbox" value="memory" checked> 内存使用率</label>
                                            <label><input type="checkbox" value="disk" checked> 磁盘使用率</label>
                                            <label><input type="checkbox" value="alerts" checked> 告警统计</label>
                                            <label><input type="checkbox" value="hosts" checked> 主机状态</label>
                                        </div>
                                    </div>
                                    <button class="btn btn-primary" onclick="generateCustomReport()">
                                        <i class="bi bi-file-earmark-text"></i> 生成报表
                                    </button>
                                </div>
                                <div class="custom-report-result" id="custom-report-result">
                                    <!-- 自定义报表结果将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 远程终端视图 -->
            <div class="terminal-view" id="terminal-view" style="display: none;">
                <div class="view-header">
                    <h2>💻 远程终端</h2>
                    <p>远程执行命令和脚本</p>
                    <div class="header-actions">
                        <button class="new-terminal-btn" onclick="showNewTerminalModal()">
                            <i class="bi bi-plus-circle"></i> 新建终端
                        </button>
                        <button class="refresh-btn" onclick="refreshTerminals()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 终端标签页 -->
                    <div class="terminal-tabs" id="terminal-tabs">
                        <div class="tabs-container">
                            <!-- 终端标签将通过JavaScript动态生成 -->
                        </div>
                        <button class="add-tab-btn" onclick="showNewTerminalModal()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>

                    <!-- 终端容器 -->
                    <div class="terminal-container" id="terminal-container">
                        <!-- 欢迎界面 -->
                        <div class="terminal-welcome" id="terminal-welcome">
                            <div class="welcome-content">
                                <i class="bi bi-terminal"></i>
                                <h3>欢迎使用远程终端</h3>
                                <p>点击"新建终端"按钮开始连接到远程主机</p>
                                <button class="btn btn-primary" onclick="showNewTerminalModal()">
                                    <i class="bi bi-plus-circle"></i> 新建终端
                                </button>
                            </div>
                        </div>

                        <!-- 终端实例将通过JavaScript动态生成 -->
                    </div>

                    <!-- 终端工具栏 -->
                    <div class="terminal-toolbar" id="terminal-toolbar" style="display: none;">
                        <div class="toolbar-left">
                            <button class="tool-btn" onclick="clearCurrentTerminal()" title="清屏">
                                <i class="bi bi-trash"></i>
                            </button>
                            <button class="tool-btn" onclick="copyTerminalContent()" title="复制">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="tool-btn" onclick="pasteToTerminal()" title="粘贴">
                                <i class="bi bi-clipboard-plus"></i>
                            </button>
                            <button class="tool-btn" onclick="downloadTerminalLog()" title="下载日志">
                                <i class="bi bi-download"></i>
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <span class="connection-status" id="connection-status">
                                <i class="bi bi-circle-fill"></i> 未连接
                            </span>
                            <button class="tool-btn" onclick="toggleFullscreen()" title="全屏">
                                <i class="bi bi-fullscreen"></i>
                            </button>
                            <button class="tool-btn danger" onclick="disconnectCurrentTerminal()" title="断开连接">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件管理视图 -->
            <div class="files-view" id="files-view" style="display: none;">
                <div class="view-header">
                    <h2>📁 文件管理</h2>
                    <p>管理服务器文件和配置</p>
                    <div class="header-actions">
                        <button class="upload-btn" onclick="showUploadModal()">
                            <i class="bi bi-cloud-upload"></i> 上传文件
                        </button>
                        <button class="new-folder-btn" onclick="showNewFolderModal()">
                            <i class="bi bi-folder-plus"></i> 新建文件夹
                        </button>
                        <button class="refresh-btn" onclick="refreshFileList()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 文件管理工具栏 -->
                    <div class="files-toolbar">
                        <div class="toolbar-left">
                            <!-- 主机选择 -->
                            <div class="host-selector">
                                <label>选择主机:</label>
                                <select id="file-host-select" onchange="changeFileHost()">
                                    <option value="">请选择主机</option>
                                    <!-- 主机选项将通过JavaScript动态生成 -->
                                </select>
                            </div>

                            <!-- 路径导航 -->
                            <div class="path-navigator">
                                <div class="path-breadcrumb" id="path-breadcrumb">
                                    <span class="path-item root" onclick="navigateToPath('/')">
                                        <i class="bi bi-house"></i> 根目录
                                    </span>
                                </div>
                                <div class="path-input-group">
                                    <input type="text" id="path-input" placeholder="输入路径..." onkeydown="handlePathInput(event)">
                                    <button onclick="navigateToInputPath()">
                                        <i class="bi bi-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="toolbar-right">
                            <!-- 视图切换 -->
                            <div class="view-toggle">
                                <button class="view-btn active" onclick="setFileView('list')" data-view="list">
                                    <i class="bi bi-list"></i>
                                </button>
                                <button class="view-btn" onclick="setFileView('grid')" data-view="grid">
                                    <i class="bi bi-grid"></i>
                                </button>
                            </div>

                            <!-- 排序选择 -->
                            <select id="sort-select" onchange="sortFiles()">
                                <option value="name">按名称排序</option>
                                <option value="size">按大小排序</option>
                                <option value="date">按修改时间排序</option>
                                <option value="type">按类型排序</option>
                            </select>
                        </div>
                    </div>

                    <!-- 文件列表容器 -->
                    <div class="files-container">
                        <!-- 连接提示 -->
                        <div class="files-connect-prompt" id="files-connect-prompt">
                            <div class="connect-content">
                                <i class="bi bi-server"></i>
                                <h3>选择主机开始浏览文件</h3>
                                <p>请在上方选择一个主机来浏览其文件系统</p>
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div class="files-loading" id="files-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>正在加载文件列表...</p>
                        </div>

                        <!-- 文件列表 -->
                        <div class="files-list" id="files-list" style="display: none;">
                            <!-- 文件项目将通过JavaScript动态生成 -->
                        </div>

                        <!-- 空文件夹 -->
                        <div class="files-empty" id="files-empty" style="display: none;">
                            <i class="bi bi-folder-x"></i>
                            <h3>文件夹为空</h3>
                            <p>当前文件夹中没有文件或子文件夹</p>
                        </div>
                    </div>

                    <!-- 文件操作面板 -->
                    <div class="file-operations" id="file-operations" style="display: none;">
                        <div class="operations-header">
                            <h4>文件操作</h4>
                            <button class="close-operations" onclick="closeFileOperations()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <div class="operations-content">
                            <div class="selected-file-info" id="selected-file-info">
                                <!-- 选中文件信息 -->
                            </div>
                            <div class="operations-buttons">
                                <button class="op-btn" onclick="downloadSelectedFile()">
                                    <i class="bi bi-download"></i> 下载
                                </button>
                                <button class="op-btn" onclick="editSelectedFile()">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                                <button class="op-btn" onclick="copySelectedFile()">
                                    <i class="bi bi-copy"></i> 复制
                                </button>
                                <button class="op-btn" onclick="moveSelectedFile()">
                                    <i class="bi bi-arrow-right"></i> 移动
                                </button>
                                <button class="op-btn danger" onclick="deleteSelectedFile()">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置视图 -->
            <div class="settings-view" id="settings-view" style="display: none;">
                <div class="view-header">
                    <h2>⚙️ 系统设置</h2>
                    <p>配置系统参数和偏好设置</p>
                    <div class="header-actions">
                        <button class="save-settings-btn" onclick="saveAllSettings()">
                            <i class="bi bi-check-circle"></i> 保存设置
                        </button>
                        <button class="reset-settings-btn" onclick="resetSettings()">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
                <div class="view-content">
                    <!-- 设置导航 -->
                    <div class="settings-nav">
                        <button class="settings-nav-item active" onclick="switchSettingsTab('general')" data-tab="general">
                            <i class="bi bi-gear"></i> 常规设置
                        </button>
                        <button class="settings-nav-item" onclick="switchSettingsTab('appearance')" data-tab="appearance">
                            <i class="bi bi-palette"></i> 外观设置
                        </button>
                        <button class="settings-nav-item" onclick="switchSettingsTab('notifications')" data-tab="notifications">
                            <i class="bi bi-bell"></i> 通知设置
                        </button>
                        <button class="settings-nav-item" onclick="switchSettingsTab('security')" data-tab="security">
                            <i class="bi bi-shield-check"></i> 安全设置
                        </button>
                        <button class="settings-nav-item" onclick="switchSettingsTab('advanced')" data-tab="advanced">
                            <i class="bi bi-cpu"></i> 高级设置
                        </button>
                        <button class="settings-nav-item" onclick="switchSettingsTab('about')" data-tab="about">
                            <i class="bi bi-info-circle"></i> 关于系统
                        </button>
                    </div>

                    <!-- 设置内容 -->
                    <div class="settings-content">
                        <!-- 常规设置 -->
                        <div class="settings-panel" id="general-settings" style="display: block;">
                            <h3>常规设置</h3>

                            <div class="settings-section">
                                <h4>系统配置</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>系统名称</span>
                                        <input type="text" id="system-name" value="AI运维管理平台" class="setting-input">
                                    </label>
                                    <p class="setting-description">设置系统显示名称</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>默认语言</span>
                                        <select id="system-language" class="setting-select">
                                            <option value="zh-CN" selected>简体中文</option>
                                            <option value="en-US">English</option>
                                            <option value="ja-JP">日本語</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">选择系统界面语言</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>时区设置</span>
                                        <select id="system-timezone" class="setting-select">
                                            <option value="Asia/Shanghai" selected>Asia/Shanghai (UTC+8)</option>
                                            <option value="UTC">UTC (UTC+0)</option>
                                            <option value="America/New_York">America/New_York (UTC-5)</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">设置系统时区</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>数据刷新</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>自动刷新间隔</span>
                                        <select id="refresh-interval" class="setting-select">
                                            <option value="10">10秒</option>
                                            <option value="30" selected>30秒</option>
                                            <option value="60">1分钟</option>
                                            <option value="300">5分钟</option>
                                            <option value="0">关闭自动刷新</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">设置监控数据自动刷新间隔</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="enable-realtime" checked>
                                        <span>启用实时数据</span>
                                    </label>
                                    <p class="setting-description">启用WebSocket实时数据推送</p>
                                </div>
                            </div>
                        </div>

                        <!-- 外观设置 -->
                        <div class="settings-panel" id="appearance-settings" style="display: none;">
                            <h3>外观设置</h3>

                            <div class="settings-section">
                                <h4>主题配置</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>主题模式</span>
                                        <select id="theme-mode" class="setting-select">
                                            <option value="light" selected>浅色主题</option>
                                            <option value="dark">深色主题</option>
                                            <option value="auto">跟随系统</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">选择界面主题模式</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>主色调</span>
                                        <div class="color-picker">
                                            <input type="color" id="primary-color" value="#6366f1">
                                            <span class="color-preview"></span>
                                        </div>
                                    </label>
                                    <p class="setting-description">自定义系统主色调</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>界面配置</h4>
                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="compact-mode">
                                        <span>紧凑模式</span>
                                    </label>
                                    <p class="setting-description">启用紧凑的界面布局</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="show-animations" checked>
                                        <span>界面动画</span>
                                    </label>
                                    <p class="setting-description">启用界面过渡动画效果</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>字体大小</span>
                                        <select id="font-size" class="setting-select">
                                            <option value="small">小</option>
                                            <option value="medium" selected>中</option>
                                            <option value="large">大</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">调整界面字体大小</p>
                                </div>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div class="settings-panel" id="notifications-settings" style="display: none;">
                            <h3>通知设置</h3>

                            <div class="settings-section">
                                <h4>通知配置</h4>
                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="enable-notifications" checked>
                                        <span>启用通知</span>
                                    </label>
                                    <p class="setting-description">启用系统通知功能</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="desktop-notifications" checked>
                                        <span>桌面通知</span>
                                    </label>
                                    <p class="setting-description">启用浏览器桌面通知</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="sound-notifications">
                                        <span>声音提醒</span>
                                    </label>
                                    <p class="setting-description">启用通知声音提醒</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>告警通知</h4>
                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="alert-notifications" checked>
                                        <span>告警通知</span>
                                    </label>
                                    <p class="setting-description">接收系统告警通知</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>通知级别</span>
                                        <select id="notification-level" class="setting-select">
                                            <option value="all" selected>所有级别</option>
                                            <option value="warning">警告及以上</option>
                                            <option value="critical">仅严重告警</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">设置接收通知的最低级别</p>
                                </div>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="settings-panel" id="security-settings" style="display: none;">
                            <h3>安全设置</h3>

                            <div class="settings-section">
                                <h4>会话管理</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>会话超时时间</span>
                                        <select id="session-timeout" class="setting-select">
                                            <option value="30">30分钟</option>
                                            <option value="60" selected>1小时</option>
                                            <option value="240">4小时</option>
                                            <option value="480">8小时</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">设置用户会话超时时间</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="auto-logout" checked>
                                        <span>自动登出</span>
                                    </label>
                                    <p class="setting-description">会话超时后自动登出</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>操作安全</h4>
                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="confirm-dangerous" checked>
                                        <span>危险操作确认</span>
                                    </label>
                                    <p class="setting-description">执行危险操作前需要确认</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="audit-log" checked>
                                        <span>操作审计</span>
                                    </label>
                                    <p class="setting-description">记录用户操作日志</p>
                                </div>
                            </div>
                        </div>

                        <!-- 高级设置 -->
                        <div class="settings-panel" id="advanced-settings" style="display: none;">
                            <h3>高级设置</h3>

                            <div class="settings-section">
                                <h4>性能配置</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>数据缓存时间</span>
                                        <select id="cache-duration" class="setting-select">
                                            <option value="60">1分钟</option>
                                            <option value="300" selected>5分钟</option>
                                            <option value="900">15分钟</option>
                                            <option value="1800">30分钟</option>
                                        </select>
                                    </label>
                                    <p class="setting-description">设置数据缓存有效期</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span>最大并发连接</span>
                                        <input type="number" id="max-connections" value="10" min="1" max="100" class="setting-input">
                                    </label>
                                    <p class="setting-description">设置最大并发SSH连接数</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>调试选项</h4>
                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="debug-mode">
                                        <span>调试模式</span>
                                    </label>
                                    <p class="setting-description">启用调试模式（显示详细日志）</p>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" id="performance-monitor">
                                        <span>性能监控</span>
                                    </label>
                                    <p class="setting-description">启用前端性能监控</p>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>性能统计</h4>
                                <div class="performance-stats" id="performance-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">运行时间:</span>
                                        <span class="stat-value" id="perf-uptime">0秒</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">API调用:</span>
                                        <span class="stat-value" id="perf-api-calls">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">平均响应时间:</span>
                                        <span class="stat-value" id="perf-avg-response">0ms</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">缓存命中率:</span>
                                        <span class="stat-value" id="perf-cache-hit">0%</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">错误次数:</span>
                                        <span class="stat-value" id="perf-errors">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">缓存大小:</span>
                                        <span class="stat-value" id="perf-cache-size">0</span>
                                    </div>
                                </div>
                                <div class="performance-actions">
                                    <button class="action-btn" onclick="refreshPerformanceStats()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新统计
                                    </button>
                                    <button class="action-btn" onclick="clearPerformanceStats()">
                                        <i class="bi bi-trash"></i> 清除统计
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 关于系统 -->
                        <div class="settings-panel" id="about-settings" style="display: none;">
                            <h3>关于系统</h3>

                            <div class="settings-section">
                                <h4>系统信息</h4>
                                <div class="about-info">
                                    <div class="about-item">
                                        <span class="about-label">系统名称:</span>
                                        <span class="about-value">AI运维管理平台</span>
                                    </div>
                                    <div class="about-item">
                                        <span class="about-label">版本号:</span>
                                        <span class="about-value">v1.0.0</span>
                                    </div>
                                    <div class="about-item">
                                        <span class="about-label">构建时间:</span>
                                        <span class="about-value" id="build-time">2024-08-05 16:30:00</span>
                                    </div>
                                    <div class="about-item">
                                        <span class="about-label">运行时间:</span>
                                        <span class="about-value" id="uptime">计算中...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>技术栈</h4>
                                <div class="tech-stack">
                                    <div class="tech-item">
                                        <i class="bi bi-code-slash"></i>
                                        <div>
                                            <div class="tech-name">前端技术</div>
                                            <div class="tech-desc">HTML5, CSS3, JavaScript, Chart.js</div>
                                        </div>
                                    </div>
                                    <div class="tech-item">
                                        <i class="bi bi-server"></i>
                                        <div>
                                            <div class="tech-name">后端技术</div>
                                            <div class="tech-desc">Go, Gin, GORM, SQLite</div>
                                        </div>
                                    </div>
                                    <div class="tech-item">
                                        <i class="bi bi-robot"></i>
                                        <div>
                                            <div class="tech-name">AI技术</div>
                                            <div class="tech-desc">DeepSeek API, 智能运维</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4>系统操作</h4>
                                <div class="system-actions">
                                    <button class="action-btn" onclick="exportSettings()">
                                        <i class="bi bi-download"></i> 导出配置
                                    </button>
                                    <button class="action-btn" onclick="importSettings()">
                                        <i class="bi bi-upload"></i> 导入配置
                                    </button>
                                    <button class="action-btn" onclick="clearCache()">
                                        <i class="bi bi-trash"></i> 清除缓存
                                    </button>
                                    <button class="action-btn danger" onclick="resetSystem()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置系统
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 右侧聊天历史面板 -->
        <aside class="chat-history-panel" id="chat-history-panel">
            <div class="history-header">
                <h3>聊天历史</h3>
                <button class="close-history-btn" onclick="toggleHistoryPanel()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="history-content">
                <div class="history-search">
                    <input type="text" placeholder="搜索对话..." class="history-search-input"
                           oninput="searchChatHistory(this.value)" id="history-search-input">
                </div>
                <div class="history-list" id="history-list">
                    <!-- 历史对话项目将在这里动态生成 -->
                    <div class="history-item">
                        <div class="history-item-title">服务器性能监控告警处理，CPU使用率过高...</div>
                        <div class="history-item-time">昨天 15:10</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">网络连接异常排查，ping测试失败...</div>
                        <div class="history-item-time">昨天 14:30</div>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- 🎨 加载覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loading-text">正在处理中...</div>
        </div>
    </div>

    <!-- 🎨 错误提示组件 -->
    <div class="error-toast" id="error-toast">
        <button class="toast-close" onclick="hideErrorToast()">&times;</button>
        <div class="toast-content" id="error-content"></div>
    </div>

    <!-- 🎨 成功提示组件 -->
    <div class="success-toast" id="success-toast">
        <button class="toast-close" onclick="hideSuccessToast()">&times;</button>
        <div class="toast-content" id="success-content"></div>
    </div>

    <!-- Sider风格的JavaScript功能 -->
    <script>
        // 全局变量
        let currentUser = null;
        let chatMessages = [];
        let isTyping = false;
        let currentSessionId = null;
        let currentSessionTitle = '新对话';
        let currentModel = null;
        let availableModels = [];
        let chatSessions = [];
        let currentView = 'chat';

        // 性能优化相关变量
        let performanceCache = new Map();
        let requestQueue = new Map();
        let debounceTimers = new Map();
        let throttleTimers = new Map();
        let lastRequestTime = new Map();

        // 性能监控
        const performanceMonitor = {
            startTime: Date.now(),
            metrics: {
                apiCalls: 0,
                cacheHits: 0,
                cacheMisses: 0,
                errors: 0,
                avgResponseTime: 0
            },

            recordApiCall: function(duration) {
                this.metrics.apiCalls++;
                this.metrics.avgResponseTime =
                    (this.metrics.avgResponseTime * (this.metrics.apiCalls - 1) + duration) / this.metrics.apiCalls;
            },

            recordCacheHit: function() {
                this.metrics.cacheHits++;
            },

            recordCacheMiss: function() {
                this.metrics.cacheMisses++;
            },

            recordError: function() {
                this.metrics.errors++;
            },

            getStats: function() {
                const uptime = Date.now() - this.startTime;
                const cacheHitRate = this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100;

                return {
                    uptime: Math.floor(uptime / 1000),
                    ...this.metrics,
                    cacheHitRate: isNaN(cacheHitRate) ? 0 : cacheHitRate.toFixed(2)
                };
            }
        };

        // 防抖函数
        function debounce(key, func, delay = 300) {
            if (debounceTimers.has(key)) {
                clearTimeout(debounceTimers.get(key));
            }

            const timer = setTimeout(() => {
                func();
                debounceTimers.delete(key);
            }, delay);

            debounceTimers.set(key, timer);
        }

        // 节流函数
        function throttle(key, func, delay = 1000) {
            const now = Date.now();
            const lastTime = lastRequestTime.get(key) || 0;

            if (now - lastTime >= delay) {
                lastRequestTime.set(key, now);
                func();
                return true;
            }
            return false;
        }

        // 缓存管理
        const cacheManager = {
            set: function(key, data, ttl = 300000) { // 默认5分钟TTL
                const item = {
                    data: data,
                    timestamp: Date.now(),
                    ttl: ttl
                };
                performanceCache.set(key, item);

                // 自动清理过期缓存
                setTimeout(() => {
                    if (performanceCache.has(key)) {
                        const cached = performanceCache.get(key);
                        if (Date.now() - cached.timestamp > cached.ttl) {
                            performanceCache.delete(key);
                        }
                    }
                }, ttl);
            },

            get: function(key) {
                if (!performanceCache.has(key)) {
                    performanceMonitor.recordCacheMiss();
                    return null;
                }

                const item = performanceCache.get(key);
                if (Date.now() - item.timestamp > item.ttl) {
                    performanceCache.delete(key);
                    performanceMonitor.recordCacheMiss();
                    return null;
                }

                performanceMonitor.recordCacheHit();
                return item.data;
            },

            clear: function(pattern) {
                if (pattern) {
                    for (const key of performanceCache.keys()) {
                        if (key.includes(pattern)) {
                            performanceCache.delete(key);
                        }
                    }
                } else {
                    performanceCache.clear();
                }
            },

            size: function() {
                return performanceCache.size;
            }
        };

        // 重试机制
        async function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
            for (let i = 0; i < maxRetries; i++) {
                try {
                    return await requestFn();
                } catch (error) {
                    if (i === maxRetries - 1) throw error;

                    console.warn(`请求失败，${delay}ms后重试 (${i + 1}/${maxRetries}):`, error.message);
                    await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i))); // 指数退避
                }
            }
        }

        // 优化的API请求函数（带重试和错误处理）
        async function optimizedFetch(url, options = {}, cacheKey = null, cacheTTL = 300000, enableRetry = true) {
            const startTime = Date.now();

            try {
                // 检查缓存
                if (cacheKey && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {
                    const cached = cacheManager.get(cacheKey);
                    if (cached) {
                        return cached;
                    }
                }

                // 检查是否有相同请求正在进行
                const requestKey = `${url}_${JSON.stringify(options)}`;
                if (requestQueue.has(requestKey)) {
                    return await requestQueue.get(requestKey);
                }

                // 请求函数
                const makeRequest = async () => {
                    const response = await fetch(url, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        timeout: 30000, // 30秒超时
                        ...options
                    });

                    const duration = Date.now() - startTime;
                    performanceMonitor.recordApiCall(duration);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();

                    // 缓存GET请求结果
                    if (cacheKey && (!options.method || options.method === 'GET')) {
                        cacheManager.set(cacheKey, data, cacheTTL);
                    }

                    return data;
                };

                // 发起请求（带重试）
                const requestPromise = enableRetry ?
                    retryRequest(makeRequest, 3, 1000) :
                    makeRequest();

                requestQueue.set(requestKey, requestPromise);

                const result = await requestPromise;
                requestQueue.delete(requestKey);

                return result;

            } catch (error) {
                performanceMonitor.recordError();
                requestQueue.delete(`${url}_${JSON.stringify(options)}`);

                // 根据错误类型提供更友好的错误信息
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    throw new Error('网络连接失败，请检查网络连接');
                } else if (error.message.includes('timeout')) {
                    throw new Error('请求超时，请稍后重试');
                } else if (error.message.includes('404')) {
                    throw new Error('请求的资源不存在');
                } else if (error.message.includes('500')) {
                    throw new Error('服务器内部错误');
                } else {
                    throw error;
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // 模型管理功能 - 提前定义
        async function loadAvailableModels() {
            try {
                console.log('🔄 开始加载可用模型...');
                const response = await fetch('/api/v1/models/available');
                const result = await response.json();

                if (result.success) {
                    availableModels = result.data.models;
                    currentModel = result.data.models.find(m => m.is_current) || result.data.models[0];
                    console.log('✅ 模型加载成功:', currentModel);
                    updateModelSelector();
                    updateModelDropdown();
                } else {
                    console.error('Failed to load models:', result.error);
                    // 使用默认模型
                    currentModel = {
                        name: 'deepseek-chat',
                        display_name: 'DeepSeek Chat',
                        icon: '🤖',
                        description: 'DeepSeek智能对话模型'
                    };
                    updateModelSelector();
                }
            } catch (error) {
                console.error('Error loading models:', error);
                // 使用默认模型
                currentModel = {
                    name: 'deepseek-chat',
                    display_name: 'DeepSeek Chat',
                    icon: '🤖',
                    description: 'DeepSeek智能对话模型'
                };
                updateModelSelector();
            }
        }

        function updateModelSelector() {
            console.log('🔄 更新模型选择器，当前模型:', currentModel);
            if (currentModel) {
                const modelIcon = document.getElementById('current-model-icon');
                const modelName = document.getElementById('current-model-name');

                if (modelIcon) {
                    modelIcon.textContent = currentModel.icon || '🤖';
                    console.log('✅ 更新模型图标:', currentModel.icon);
                }
                if (modelName) {
                    modelName.textContent = currentModel.display_name || currentModel.name;
                    console.log('✅ 更新模型名称:', currentModel.display_name || currentModel.name);
                }
            } else {
                console.warn('⚠️ currentModel 为空，无法更新模型选择器');
            }
        }

        function updateModelDropdown() {
            const dropdown = document.getElementById('model-dropdown-content');
            if (!dropdown || !availableModels.length) return;

            dropdown.innerHTML = availableModels.map(model => `
                <div class="model-option ${model.is_current ? 'current' : ''}"
                     onclick="switchModel('${model.name}')">
                    <div class="model-option-icon">${model.icon || '🤖'}</div>
                    <div class="model-option-info">
                        <div class="model-option-name">${model.display_name}</div>
                        <div class="model-option-desc">${model.description || ''}</div>
                    </div>
                    <div class="model-option-provider">${model.provider}</div>
                </div>
            `).join('');
        }

        function toggleModelSelector() {
            const dropdown = document.getElementById('model-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            }
        }

        async function switchModel(modelName) {
            try {
                const response = await fetch('/api/v1/models/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ model_name: modelName })
                });

                const result = await response.json();

                if (result.success) {
                    currentModel = result.data;
                    updateModelSelector();

                    // 更新下拉菜单中的当前状态
                    availableModels.forEach(model => {
                        model.is_current = model.name === modelName;
                    });
                    updateModelDropdown();

                    // 隐藏下拉菜单
                    toggleModelSelector();

                    console.log('Model switched to:', currentModel.display_name);
                } else {
                    console.error('Failed to switch model:', result.error);
                    alert('切换模型失败: ' + result.error);
                }
            } catch (error) {
                console.error('Error switching model:', error);
                alert('切换模型时发生错误');
            }
        }

        // 初始化应用
        function initializeApp() {
            console.log('Initializing Sider-style AI Operations Platform...');
            initializeChat();
            setupEventListeners();
            connectWebSocket();
            loadAvailableModels();

            // 延迟强制更新模型选择器，确保DOM已加载
            setTimeout(() => {
                if (currentModel) {
                    console.log('🔄 强制更新模型选择器');
                    updateModelSelector();
                }
            }, 1000);
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    startNewChat();
                }
            });
        }

        // 视图切换功能
        function switchToChat() {
            switchView('chat');
        }

        function switchToHosts() {
            switchView('hosts');
        }

        function switchToMonitoring() {
            switchView('monitoring');
        }

        function switchToAlerts() {
            switchView('alerts');
        }

        function switchToReports() {
            switchView('reports');
        }

        function switchToTerminal() {
            switchView('terminal');
        }

        function switchToFiles() {
            switchView('files');
        }

        function switchToSettings() {
            switchView('settings');
        }

        function switchView(viewName) {
            // 停止之前视图的监控
            if (currentView === 'hosts') {
                stopHostMonitoring();
            }

            // 获取当前显示的视图
            const currentViewElement = document.querySelector('.view:not([style*="display: none"])');
            const targetView = document.getElementById(viewName + '-view');

            if (!targetView) return;

            // 如果是同一个视图，不需要切换
            if (currentViewElement === targetView) return;

            // 使用动画切换视图
            if (currentViewElement && systemSettings.appearance.showAnimations) {
                // 淡出当前视图
                UXEnhancer.addAnimation(currentViewElement, 'fade-out', 300);

                setTimeout(() => {
                    // 隐藏所有视图
                    const views = ['chat', 'hosts', 'monitoring', 'alerts', 'reports', 'terminal', 'files', 'settings'];
                    views.forEach(view => {
                        const element = document.getElementById(view + '-view');
                        if (element) {
                            element.style.display = 'none';
                        }
                    });

                    // 显示目标视图
                    targetView.style.display = 'flex';

                    // 淡入新视图
                    UXEnhancer.addAnimation(targetView, 'fade-in', 300);
                }, 300);
            } else {
                // 无动画切换
                const views = ['chat', 'hosts', 'monitoring', 'alerts', 'reports', 'terminal', 'files', 'settings'];
                views.forEach(view => {
                    const element = document.getElementById(view + '-view');
                    if (element) {
                        element.style.display = 'none';
                    }
                });

                targetView.style.display = 'flex';
            }

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeNavItem = document.querySelector(`.nav-item[onclick*="${viewName}"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }

            currentView = viewName;
        }

        // 初始化聊天功能
        function initializeChat() {
            console.log('Initializing chat...');

            // 如果没有当前会话ID，创建一个新的
            if (!currentSessionId) {
                currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                currentSessionTitle = '新对话';
                chatMessages = [];
            }

            showWelcomeScreen();
            console.log('Chat initialized with session:', currentSessionId);
        }



        // 显示欢迎界面
        function showWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'flex';
            }
        }

        // 隐藏欢迎界面
        function hideWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'none';
            }
        }

        // 插入建议文本
        function insertSuggestion(text) {
            const input = document.getElementById('chat-input');
            if (input) {
                input.value = text;
                input.focus();
                adjustTextareaHeight(input);
            }
        }

        // 自动调整文本框高度
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 处理输入框按键事件
        function handleInputKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 🎨 优化：发送消息
        function sendMessage() {
            console.log('🚀 sendMessage function called!');

            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            console.log('📝 Message content:', message);

            if (!message) {
                console.log('❌ Empty message, returning');
                showErrorToast('请输入消息内容');
                return;
            }

            // 防止重复发送
            if (isWaitingForResponse) {
                console.log('⏳ Already waiting for response, returning');
                showErrorToast('请等待当前消息处理完成');
                return;
            }

            console.log('✅ Starting message send process...');

            // 隐藏欢迎界面
            hideWelcomeScreen();

            // 添加用户消息（不显示发送状态）
            const userMessageElement = addMessage('user', message);
            // 移除发送状态显示，直接进入AI处理流程

            // 清空输入框
            input.value = '';
            adjustTextareaHeight(input);

            // 不显示全屏加载，使用分步骤显示
            // showLoading('AI正在思考中...');

            // 显示打字指示器
            showTypingIndicator();

            // 🔧 设置等待状态 - 增强UI反馈
            console.log('🔧 About to call setWaitingState(true)');
            setWaitingState(true);

            // 显示AI正在输入
            console.log('🔧 About to call showTypingIndicator()');
            showTypingIndicator();

            // 设置超时机制（120秒，与后端配置保持一致）
            typingTimeout = setTimeout(() => {
                setWaitingState(false);
                hideTypingIndicator();
                addMessage('assistant', '⏰ 抱歉，AI响应超时，请重试。系统可能正在处理复杂请求，请稍后再试。');
            }, 120000);

            // 发送到后端
            console.log('🔧 About to send WebSocket message');
            if (!sendWebSocketMessage(message)) {
                // WebSocket不可用时的处理
                console.log('❌ WebSocket send failed');
                setWaitingState(false);
                hideTypingIndicator();
                addMessage('assistant', '🔌 连接失败，请检查网络连接后重试。');
            } else {
                console.log('✅ WebSocket message sent successfully');
            }
        }

        // 🎨 优化：添加消息到聊天界面（返回消息元素）
        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('chat-messages');

            // 创建消息元素
            const messageDiv = document.createElement('div');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            messageDiv.id = messageId;
            messageDiv.className = `message ${sender}`;

            // 如果是AI消息，添加头像和模型信息
            if (sender === 'assistant') {
                const messageHeader = document.createElement('div');
                messageHeader.className = 'message-header';
                messageHeader.innerHTML = `
                    <div class="message-avatar">
                        <i class="bi bi-robot"></i>
                    </div>
                    <span class="message-sender">${currentModel ? currentModel.display_name : 'AI助手'}</span>
                `;
                messageDiv.appendChild(messageHeader);
            }

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = formatMessageContent(content);

            // 🔧 新增：消息操作按钮
            const messageActions = document.createElement('div');
            messageActions.className = 'message-actions';

            if (sender === 'assistant') {
                messageActions.innerHTML = `
                    <button class="action-btn copy-btn" onclick="copyMessage('${messageId}')" title="复制消息">
                        <i class="bi bi-clipboard"></i>
                    </button>
                    <button class="action-btn regenerate-btn" onclick="regenerateResponse('${messageId}')" title="重新生成">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                `;
            } else {
                messageActions.innerHTML = `
                    <button class="action-btn edit-btn" onclick="editMessage('${messageId}')" title="编辑消息">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="action-btn resend-btn" onclick="resendMessage('${messageId}')" title="重新发送">
                        <i class="bi bi-arrow-up"></i>
                    </button>
                    <button class="action-btn copy-btn" onclick="copyMessage('${messageId}')" title="复制消息">
                        <i class="bi bi-clipboard"></i>
                    </button>
                `;
            }

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageActions);
            messagesContainer.appendChild(messageDiv);

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 保存到历史记录
            chatMessages.push({
                sender: sender,
                content: content,
                timestamp: new Date().toISOString()
            });

            // 自动保存会话
            if (chatMessages.length % 3 === 0) {
                saveCurrentSession();
            }

            // 🎨 优化：返回消息元素以便添加状态指示器
            return messageDiv;
        }

        // 格式化消息内容
        function formatMessageContent(content) {
            // 转义HTML并处理换行
            const escapeHtml = (text) => {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            let formatted = escapeHtml(content).replace(/\n/g, '<br>');

            // 处理代码块
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
                return `<div class="code-block"><pre><code>${code.trim()}</code></pre></div>`;
            });

            // 处理行内代码
            formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

            return formatted;
        }

        // 显示输入指示器
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');

            // 移除现有的输入指示器
            const existingIndicator = document.getElementById('typing-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            const typingDiv = document.createElement('div');
            typingDiv.id = 'typing-indicator';
            typingDiv.className = 'message assistant typing-message';
            typingDiv.innerHTML = `
                <div class="message-avatar">
                    <span class="model-icon">🤖</span>
                </div>
                <div class="message-content">
                    <div class="typing-status">
                        <div class="typing-animation">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">🔍 AI正在分析您的请求...</span>
                    </div>
                    <div class="typing-progress">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <span class="progress-text">步骤 1/4 - 意图识别</span>
                    </div>
                    <div class="typing-steps">
                        <div class="step active" id="step-1">
                            <span class="step-icon">🔍</span>
                            <span class="step-text">意图识别</span>
                        </div>
                        <div class="step" id="step-2">
                            <span class="step-icon">⚡</span>
                            <span class="step-text">命令生成</span>
                        </div>
                        <div class="step" id="step-3">
                            <span class="step-icon">🔧</span>
                            <span class="step-text">执行处理</span>
                        </div>
                        <div class="step" id="step-4">
                            <span class="step-icon">🧠</span>
                            <span class="step-text">结果分析</span>
                        </div>
                        <!-- 🔧 移除第5步智能渲染 -->
                    </div>
                </div>
            `;

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 🔧 启动进度动画和状态更新
            startTypingAnimation();
        }

        // 隐藏输入指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }

            // 🔧 清理动画定时器
            clearTypingAnimation();

            // 🔧 关键修复：清理AI处理超时定时器
            if (window.aiProcessingTimeout) {
                clearTimeout(window.aiProcessingTimeout);
                window.aiProcessingTimeout = null;
            }

            // 清理等待状态
            setWaitingState(false);
            if (typingTimeout) {
                clearTimeout(typingTimeout);
                typingTimeout = null;
            }
        }

        // 🔧 新增：设置等待状态的UI反馈
        function setWaitingState(waiting) {
            console.log('🔧 setWaitingState called with:', waiting);
            isWaitingForResponse = waiting;

            const input = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const sendIcon = document.getElementById('send-icon');
            const loadingSpinner = document.getElementById('loading-spinner');
            const stopButton = document.getElementById('stop-button');

            console.log('🔧 Elements found:', {
                input: !!input,
                sendButton: !!sendButton,
                sendIcon: !!sendIcon,
                loadingSpinner: !!loadingSpinner,
                stopButton: !!stopButton
            });

            if (waiting) {
                console.log('🔧 Setting waiting state...');
                // 等待状态：禁用输入，显示加载和停止按钮
                if (input) {
                    input.disabled = true;
                    input.placeholder = '🤖 AI正在思考中，点击停止按钮可取消...';
                    input.style.opacity = '0.7';
                }
                if (sendButton) {
                    sendButton.style.display = 'none'; // 隐藏发送按钮
                }
                if (stopButton) {
                    stopButton.style.display = 'flex'; // 显示停止按钮
                }
            } else {
                console.log('🔧 Clearing waiting state...');
                // 恢复正常状态
                if (input) {
                    input.disabled = false;
                    input.placeholder = '向我询问任何问题...';
                    input.style.opacity = '1';
                    input.focus(); // 自动聚焦到输入框
                }
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.classList.remove('loading');
                    sendButton.style.opacity = '1';
                    sendButton.style.display = 'flex'; // 显示发送按钮
                }
                if (sendIcon) {
                    sendIcon.style.display = 'block';
                }
                if (loadingSpinner) {
                    loadingSpinner.style.display = 'none';
                }
                if (stopButton) {
                    stopButton.style.display = 'none'; // 隐藏停止按钮
                }
            }
        }

        // 🔧 新增：网络状态监控
        function startNetworkMonitoring() {
            // 定期发送ping检测网络延迟
            setInterval(() => {
                if (ws && wsConnected) {
                    lastPingTime = Date.now();
                    // 🔧 修复：使用正确的WSMessage格式
                    const pingMessage = {
                        type: 'ping',
                        data: null,
                        timestamp: new Date(lastPingTime).toISOString()
                    };
                    ws.send(JSON.stringify(pingMessage));
                    console.log('📡 发送ping消息:', pingMessage);
                }
            }, 10000); // 每10秒检测一次
        }

        // 🔧 关键修复：WebSocket心跳保持连接
        function startWebSocketHeartbeat() {
            if (window.heartbeatInterval) {
                clearInterval(window.heartbeatInterval);
            }

            window.heartbeatInterval = setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const heartbeatMessage = {
                        type: 'heartbeat',
                        data: null,
                        timestamp: new Date().toISOString()
                    };
                    ws.send(JSON.stringify(heartbeatMessage));
                    // 🔧 减少日志噪音：只在调试时输出心跳日志
                    // console.log('💓 WebSocket心跳发送');
                } else if (ws && ws.readyState === WebSocket.CLOSED) {
                    console.log('🔧 WebSocket已关闭，尝试重连');
                    connectWebSocket();
                }
            }, 30000); // 🔧 关键修复：增加到30秒心跳，减少服务器负载
        }

        function updateConnectionQuality(latency) {
            networkLatency = latency;
            if (latency < 100) {
                connectionQuality = 'excellent';
            } else if (latency < 300) {
                connectionQuality = 'good';
            } else if (latency < 800) {
                connectionQuality = 'fair';
            } else {
                connectionQuality = 'poor';
            }

            // 根据网络质量调整步骤持续时间
            switch (connectionQuality) {
                case 'excellent':
                    adaptiveStepDuration = 2000;
                    break;
                case 'good':
                    adaptiveStepDuration = 3000;
                    break;
                case 'fair':
                    adaptiveStepDuration = 4000;
                    break;
                case 'poor':
                    adaptiveStepDuration = 6000;
                    break;
            }

            updateNetworkStatusDisplay();
        }

        function updateNetworkStatusDisplay() {
            const progressText = document.querySelector('.progress-text');
            if (progressText && isWaitingForResponse) {
                const qualityIcon = {
                    'excellent': '🚀',
                    'good': '✅',
                    'fair': '⚠️',
                    'poor': '🐌'
                };

                const currentText = progressText.textContent;
                const baseText = currentText.split(' - ')[0];
                progressText.textContent = `${baseText} - ${qualityIcon[connectionQuality]} ${networkLatency}ms`;
            }
        }

        // 🔧 新增：停止AI生成
        function stopGeneration() {
            console.log('🛑 stopGeneration called');

            // 清除等待状态
            setWaitingState(false);
            hideTypingIndicator();

            // 发送停止信号到后端
            if (ws && wsConnected) {
                const stopMessage = {
                    type: 'stop_generation',
                    session_id: currentSessionId
                };
                ws.send(JSON.stringify(stopMessage));
                console.log('🛑 Stop signal sent to backend');
            }

            // 添加停止消息
            addMessage('assistant', '⏹️ 生成已停止。您可以继续提问或重新发送消息。');

            // 清理超时定时器
            if (typingTimeout) {
                clearTimeout(typingTimeout);
                typingTimeout = null;
            }
        }

        // 🔧 修复：智能响应式等待动画（跳过第5步渲染）
        function startTypingAnimation() {
            currentProcessingStep = 1;
            stepStartTime = Date.now();
            messageStartTime = Date.now();

            const steps = [
                { icon: '🔍', text: 'AI正在分析您的请求...', progress: '步骤 1/4 - 意图识别', stepId: 'step-1', expectedDuration: 2000 },
                { icon: '⚡', text: 'AI正在生成执行命令...', progress: '步骤 2/4 - 命令生成', stepId: 'step-2', expectedDuration: 3000 },
                { icon: '🔧', text: 'AI正在执行操作...', progress: '步骤 3/4 - 执行处理', stepId: 'step-3', expectedDuration: 5000 },
                { icon: '🧠', text: 'AI正在分析结果...', progress: '步骤 4/4 - 结果分析', stepId: 'step-4', expectedDuration: 4000 }
                // 🔧 移除第5步智能渲染，因为后端已跳过
            ];

            // 🔧 修复：增加超时时间到90秒，避免与正常处理冲突
            if (window.aiProcessingTimeout) {
                clearTimeout(window.aiProcessingTimeout);
            }
            window.aiProcessingTimeout = setTimeout(() => {
                console.log('🔧 AI处理超时，强制清除等待状态');
                waitingForResponse = false;
                lastMessageId = null;
                hideTypingIndicator();
                addMessage('assistant', '⚠️ AI处理超时，请重新发送消息或检查网络连接');
            }, 90000); // 90秒超时，给复杂操作更多时间

            const typingTextElement = document.querySelector('.typing-text');
            const progressTextElement = document.querySelector('.progress-text');

            if (!typingTextElement || !progressTextElement) return;

            // 初始显示第一步
            updateStepDisplay(steps[0], typingTextElement, progressTextElement);
            updateStepProgress(1);

            // 智能步骤更新定时器
            const statusInterval = setInterval(() => {
                if (!document.getElementById('typing-indicator')) {
                    clearInterval(statusInterval);
                    return;
                }

                const currentTime = Date.now();
                const stepElapsed = currentTime - stepStartTime;
                const totalElapsed = currentTime - messageStartTime;

                // 根据网络质量和实际耗时智能调整步骤切换
                const currentStepData = steps[currentProcessingStep - 1];
                const shouldAdvance = stepElapsed > Math.max(currentStepData.expectedDuration, adaptiveStepDuration);

                if (shouldAdvance && currentProcessingStep < steps.length) {
                    currentProcessingStep++;
                    stepStartTime = currentTime;

                    const stepData = steps[currentProcessingStep - 1];
                    updateStepDisplay(stepData, typingTextElement, progressTextElement);
                    updateStepProgress(currentProcessingStep);
                }

                // 更新网络状态显示
                updateNetworkStatusDisplay();

                // 添加总耗时显示
                const totalSeconds = Math.floor(totalElapsed / 1000);
                const timeDisplay = totalSeconds > 0 ? ` (${totalSeconds}s)` : '';
                if (progressTextElement.textContent && !progressTextElement.textContent.includes('(')) {
                    progressTextElement.textContent += timeDisplay;
                }

            }, 500); // 更频繁的更新以提供更好的响应性

            // 存储定时器ID以便清理
            if (!window.typingAnimationTimers) {
                window.typingAnimationTimers = [];
            }
            window.typingAnimationTimers.push(statusInterval);
        }

        // 🔧 新增：更新步骤显示
        function updateStepDisplay(stepData, typingTextElement, progressTextElement) {
            if (typingTextElement) {
                typingTextElement.textContent = stepData.text;
            }
            if (progressTextElement) {
                progressTextElement.textContent = stepData.progress;
            }
        }

        // 🔧 修复：更新步骤进度（只处理4个步骤）
        function updateStepProgress(activeStep) {
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step-${i}`);
                if (stepElement) {
                    stepElement.classList.remove('active', 'completed');
                    if (i < activeStep) {
                        stepElement.classList.add('completed');
                    } else if (i === activeStep) {
                        stepElement.classList.add('active');
                    }
                }
            }
        }

        // 🔧 新增：清理等待动画
        function clearTypingAnimation() {
            if (window.typingAnimationTimers) {
                window.typingAnimationTimers.forEach(timer => clearInterval(timer));
                window.typingAnimationTimers = [];
            }
        }

        // 🔧 新增：消息操作功能
        function copyMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            const contentElement = messageElement.querySelector('.message-content');
            const text = contentElement.textContent || contentElement.innerText;

            navigator.clipboard.writeText(text).then(() => {
                showToast('消息已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showToast('复制失败', 'error');
            });
        }

        function editMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            const contentElement = messageElement.querySelector('.message-content');
            const currentText = contentElement.textContent || contentElement.innerText;

            // 创建编辑界面
            const editContainer = document.createElement('div');
            editContainer.className = 'edit-container';
            editContainer.innerHTML = `
                <textarea class="edit-textarea" rows="3">${currentText}</textarea>
                <div class="edit-actions">
                    <button class="btn btn-primary" onclick="saveEdit('${messageId}')">保存</button>
                    <button class="btn btn-secondary" onclick="cancelEdit('${messageId}')">取消</button>
                </div>
            `;

            // 替换内容
            contentElement.style.display = 'none';
            messageElement.insertBefore(editContainer, contentElement.nextSibling);

            // 聚焦到文本框
            const textarea = editContainer.querySelector('.edit-textarea');
            textarea.focus();
            textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        }

        function saveEdit(messageId) {
            const messageElement = document.getElementById(messageId);
            const editContainer = messageElement.querySelector('.edit-container');
            const textarea = editContainer.querySelector('.edit-textarea');
            const newText = textarea.value.trim();

            if (!newText) {
                showToast('消息不能为空', 'error');
                return;
            }

            // 更新消息内容
            const contentElement = messageElement.querySelector('.message-content');
            contentElement.innerHTML = formatMessageContent(newText);
            contentElement.style.display = 'block';

            // 移除编辑界面
            editContainer.remove();

            // 重新发送消息
            resendMessage(messageId, newText);

            showToast('消息已更新并重新发送', 'success');
        }

        function cancelEdit(messageId) {
            const messageElement = document.getElementById(messageId);
            const editContainer = messageElement.querySelector('.edit-container');
            const contentElement = messageElement.querySelector('.message-content');

            contentElement.style.display = 'block';
            editContainer.remove();
        }

        function resendMessage(messageId, customText = null) {
            const messageElement = document.getElementById(messageId);
            const contentElement = messageElement.querySelector('.message-content');
            const text = customText || contentElement.textContent || contentElement.innerText;

            if (!text.trim()) {
                showToast('消息内容为空', 'error');
                return;
            }

            // 发送消息
            if (isWaitingForResponse) {
                showToast('请等待当前请求完成', 'warning');
                return;
            }

            sendMessageWithText(text);
            showToast('消息已重新发送', 'info');
        }

        function regenerateResponse(messageId) {
            // 找到对应的用户消息
            const assistantElement = document.getElementById(messageId);
            const messagesContainer = document.getElementById('chat-messages');
            const messages = Array.from(messagesContainer.children);
            const assistantIndex = messages.indexOf(assistantElement);

            if (assistantIndex > 0) {
                const userMessage = messages[assistantIndex - 1];
                if (userMessage.classList.contains('user')) {
                    const userText = userMessage.querySelector('.message-content').textContent;

                    if (isWaitingForResponse) {
                        showToast('请等待当前请求完成', 'warning');
                        return;
                    }

                    // 移除当前AI响应
                    assistantElement.remove();

                    // 重新发送用户消息
                    sendMessageWithText(userText);
                    showToast('正在重新生成响应...', 'info');
                }
            }
        }

        function sendMessageWithText(text) {
            // 隐藏欢迎界面
            hideWelcomeScreen();

            // 设置等待状态
            setWaitingState(true);
            showTypingIndicator();

            // 设置超时机制（120秒，与后端配置保持一致）
            typingTimeout = setTimeout(() => {
                setWaitingState(false);
                hideTypingIndicator();
                addMessage('assistant', '⏰ 抱歉，AI响应超时，请重试。');
            }, 120000);

            // 发送到后端
            if (!sendWebSocketMessage(text)) {
                setWaitingState(false);
                hideTypingIndicator();
                addMessage('assistant', '🔌 连接失败，请检查网络连接后重试。');
            }
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // WebSocket连接管理
        let ws = null;
        let wsConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        // 🔧 彻底修复：添加消息等待状态跟踪
        let waitingForResponse = false;
        let lastMessageId = null;
        let typingTimeout = null;
        let isWaitingForResponse = false;

        // 🔧 新增：性能和状态监控
        let networkLatency = 0;
        let lastPingTime = 0;
        let connectionQuality = 'good'; // good, fair, poor
        let currentProcessingStep = 1;
        let stepStartTime = Date.now();
        let adaptiveStepDuration = 3000; // 自适应步骤持续时间
        let messageStartTime = 0;

        // 建立WebSocket连接
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/chat?session_id=${currentSessionId}`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    console.log('WebSocket连接已建立');
                    wsConnected = true;
                    reconnectAttempts = 0;

                    // 🔧 修复：增加等待时间，避免误判正常处理为超时
                    if (waitingForResponse) {
                        console.log('🔧 检测到未完成的请求，等待120秒后清除等待状态');
                        setTimeout(() => {
                            if (waitingForResponse) {
                                console.log('🔧 120秒后仍在等待，强制清除等待状态');
                                waitingForResponse = false;
                                lastMessageId = null;
                                hideTypingIndicator();
                                addMessage('assistant', '⚠️ 响应超时，请重新发送消息');
                            }
                        }, 120000); // 增加到120秒，与AI处理超时时间一致
                    }

                    // 🔧 启动网络监控
                    startNetworkMonitoring();

                    // 🔧 关键修复：启动WebSocket心跳保持连接
                    startWebSocketHeartbeat();
                };

                ws.onmessage = function(event) {
                    try {
                        console.log('🔧 Raw WebSocket message received:', event.data);
                        const data = JSON.parse(event.data);
                        console.log('🔧 Parsed WebSocket data:', data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('解析WebSocket消息失败:', error, 'Raw data:', event.data);
                    }
                };

                ws.onclose = function(event) {
                    console.log('🔧 WebSocket连接已关闭，代码:', event.code, '原因:', event.reason);
                    wsConnected = false;

                    // 🔧 彻底修复：如果正在等待AI响应，记录状态但不立即清除
                    if (waitingForResponse) {
                        console.log('🔧 WebSocket断开时正在等待AI响应，消息ID:', lastMessageId);
                        // 不立即清除，给重连一个机会
                    } else {
                        const typingIndicator = document.querySelector('.typing-indicator');
                        if (typingIndicator && typingIndicator.style.display !== 'none') {
                            console.log('🔧 WebSocket断开时有等待动画，清除等待状态');
                            hideTypingIndicator();
                            addMessage('assistant', '⚠️ 连接中断，请重新发送消息');
                        }
                    }

                    // 🔧 彻底修复：延迟重连，避免在AI处理期间频繁重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        const delay = Math.min(5000 * reconnectAttempts, 30000); // 最大30秒延迟
                        setTimeout(() => {
                            console.log(`🔧 尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})，延迟${delay}ms`);
                            connectWebSocket();
                        }, delay);
                    }
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    wsConnected = false;
                };

            } catch (error) {
                console.error('创建WebSocket连接失败:', error);
                wsConnected = false;
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            console.log('🔧 handleWebSocketMessage called with:', {
                type: data.type,
                hasData: !!data.data,
                dataKeys: data.data ? Object.keys(data.data) : [],
                timestamp: data.timestamp
            });

            // 🔧 关键修复：只在收到AI响应时才清除等待状态
            // hideTypingIndicator(); // 移除这里的无条件调用

            switch (data.type) {
                case 'connected':
                    console.log('WebSocket连接确认:', data.data);
                    break;

                case 'user_message':
                    // 用户消息确认，通常不需要处理
                    break;

                case 'assistant_message':
                    // 🎨 优化：AI助手响应 - 立即清除等待状态和UI指示器
                    console.log('🔧 Received assistant_message:', {
                        hasData: !!data.data,
                        hasContent: !!(data.data && data.data.content),
                        contentLength: data.data && data.data.content ? data.data.content.length : 0,
                        dataStructure: data.data ? Object.keys(data.data) : [],
                        waitingForResponse: waitingForResponse,
                        lastMessageId: lastMessageId
                    });

                    // 🎨 优化：彻底修复：清除等待状态和UI指示器
                    waitingForResponse = false;
                    lastMessageId = null;

                    // 🎨 优化：强制清除所有等待动画和定时器
                    clearTypingAnimation();
                    hideTypingIndicator();
                    // hideLoading(); // 不使用全屏加载
                    console.log('🔧 Typing indicator hidden after assistant_message');

                    // 🎨 优化：更新用户消息状态为已发送
                    const userMessages = document.querySelectorAll('.user-message');
                    if (userMessages.length > 0) {
                        const lastUserMessage = userMessages[userMessages.length - 1];
                        addMessageStatus(lastUserMessage, 'sent');
                    }

                    if (data.data && data.data.content) {
                        addMessage('assistant', data.data.content);

                        // 🔧 记录成功接收响应的时间
                        console.log('✅ AI响应成功接收，内容长度:', data.data.content.length);

                        // 🎨 优化：显示成功提示
                        showSuccessToast('AI响应已接收');

                        // 更新会话标题
                        if (currentSessionTitle === '新对话' && chatMessages.length <= 2) {
                            const lastUserMessage = chatMessages.find(m => m.sender === 'user');
                            if (lastUserMessage) {
                                currentSessionTitle = lastUserMessage.content.substring(0, 20) +
                                    (lastUserMessage.content.length > 20 ? '...' : '');
                            }
                        }
                    } else {
                        console.error('❌ assistant_message 缺少内容:', data);
                        hideTypingIndicator(); // 确保清除等待状态
                        // hideLoading(); // 不使用全屏加载

                        // 🎨 优化：显示错误提示
                        showErrorToast('AI响应内容不完整，请重试');

                        // 🎨 优化：更新用户消息状态为错误
                        const userMessages = document.querySelectorAll('.user-message');
                        if (userMessages.length > 0) {
                            const lastUserMessage = userMessages[userMessages.length - 1];
                            addMessageStatus(lastUserMessage, 'error');
                        }

                        addMessage('assistant', '抱歉，我没有收到完整的响应内容。请重新发送您的消息。');
                    }
                    break;

                case 'error':
                    console.error('AI处理错误:', data.message);
                    console.log('🔧 Received error, hiding typing indicator');
                    hideTypingIndicator();
                    addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。');
                    break;

                case 'pong':
                    // 网络延迟响应
                    if (data.timestamp && lastPingTime) {
                        // 🔧 修复：正确解析时间戳
                        let responseTime;
                        if (typeof data.timestamp === 'string') {
                            responseTime = new Date(data.timestamp).getTime();
                        } else {
                            responseTime = data.timestamp;
                        }
                        const latency = Date.now() - lastPingTime;
                        updateConnectionQuality(latency);

                        // 🔧 新增：更新性能监控器的网络延迟
                        if (window.performanceMonitor) {
                            window.performanceMonitor.updateNetworkLatency(latency);
                        }

                        console.log('📡 收到pong响应，延迟:', latency + 'ms');
                    } else if (data.timestamp) {
                        // 🔧 新增：处理性能监控器发送的ping
                        const latency = performance.now() - data.timestamp;
                        if (window.performanceMonitor) {
                            window.performanceMonitor.updateNetworkLatency(latency);
                        }
                        console.log('📊 性能监控器延迟测试:', latency + 'ms');
                    }
                    break;

                case 'metric_update':
                    // 处理系统指标更新
                    handleMetricUpdate(data.data);
                    break;

                default:
                    console.log('未知消息类型:', data.type);
            }
        }

        // 处理系统指标更新
        function handleMetricUpdate(metricData) {
            if (!metricData) return;

            console.log('收到系统指标更新:', metricData);

            // 这里可以添加指标显示逻辑，比如更新仪表板
            // 暂时只记录日志，避免控制台错误
            try {
                // 如果有指标显示区域，可以在这里更新
                const metricsPanel = document.getElementById('metrics-panel');
                if (metricsPanel) {
                    updateMetricsDisplay(metricData);
                }
            } catch (error) {
                console.warn('更新指标显示失败:', error);
            }
        }

        // 更新指标显示
        function updateMetricsDisplay(metricData) {
            // 实现指标显示逻辑
            const { host_name, metric_type, value, unit, timestamp } = metricData;
            console.log(`主机 ${host_name} 的 ${metric_type} 指标: ${value}${unit || ''}`);
        }

        // 发送WebSocket消息
        function sendWebSocketMessage(message) {
            if (ws && wsConnected) {
                // 🔧 彻底修复：生成消息ID并跟踪等待状态
                lastMessageId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                waitingForResponse = true;

                const data = {
                    type: 'message',
                    content: message,
                    messageId: lastMessageId // 添加消息ID
                };

                console.log('🔧 发送消息，ID:', lastMessageId, '内容:', message);
                ws.send(JSON.stringify(data));
                return true;
            }
            return false;
        }

        // 开始新对话
        function startNewChat() {
            // 清空当前对话
            const messagesContainer = document.getElementById('chat-messages');
            if (messagesContainer) {
                messagesContainer.innerHTML = '';
            }

            // 重置会话
            currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            currentSessionTitle = '新对话';
            chatMessages = [];

            // 显示欢迎界面
            showWelcomeScreen();

            console.log('Started new chat with session:', currentSessionId);
        }

        // 切换历史面板显示
        function toggleHistoryPanel() {
            const panel = document.getElementById('chat-history-panel');
            panel.classList.toggle('open');

            // 如果面板打开，加载历史记录
            if (panel.classList.contains('open')) {
                loadChatHistory();
            }
        }

        // 🎨 优化：加载聊天历史到面板
        function loadChatHistory() {
            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const historyList = document.getElementById('history-list');

            if (sessions.length === 0) {
                historyList.innerHTML = '<div class="no-history">暂无聊天历史<br><small>开始新对话来创建历史记录</small></div>';
                return;
            }

            // 按更新时间排序（最新的在前）
            const sortedSessions = sessions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

            historyList.innerHTML = sortedSessions.map(session => {
                const date = new Date(session.updatedAt);
                const now = new Date();
                const diffTime = Math.abs(now - date);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                // 格式化时间显示
                let timeDisplay;
                if (diffDays === 1) {
                    timeDisplay = '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays === 2) {
                    timeDisplay = '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays <= 7) {
                    timeDisplay = diffDays + '天前';
                } else {
                    timeDisplay = date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
                }

                // 截取标题，避免过长
                const title = session.title.length > 50 ? session.title.substring(0, 50) + '...' : session.title;

                return `
                    <div class="history-item" title="${session.title}">
                        <div class="history-item-content" onclick="loadSession('${session.id}')">
                            <div class="history-item-title">${title}</div>
                            <div class="history-item-time">${timeDisplay}</div>
                        </div>
                        <div class="history-item-actions">
                            <button class="history-delete-btn" onclick="deleteSession('${session.id}', event)" title="删除对话">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 🔧 关键修复：加载指定会话
        function loadSession(sessionId) {
            console.log('🔧 Loading session:', sessionId);

            // 从localStorage获取会话数据
            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const session = sessions.find(s => s.id === sessionId);

            if (!session) {
                console.error('Session not found:', sessionId);
                addMessage('assistant', '⚠️ 会话不存在或已被删除');
                return;
            }

            // 清空当前聊天
            chatMessages = [];
            updateChatDisplay();

            // 加载会话消息
            if (session.messages && session.messages.length > 0) {
                chatMessages = session.messages;
                updateChatDisplay();

                // 更新会话标题
                currentSessionTitle = session.title;

                // 滚动到底部
                setTimeout(() => {
                    const chatContainer = document.getElementById('chat-messages');
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }, 100);

                console.log('✅ Session loaded successfully:', session.title);
                addMessage('assistant', `📋 已加载会话：${session.title}`);
            } else {
                addMessage('assistant', '📋 会话已加载，但没有历史消息');
            }

            // 关闭历史面板
            toggleHistoryPanel();

            // 更新当前会话ID
            currentSessionId = sessionId;
        }

        // 🔍 搜索聊天历史
        function searchChatHistory(searchTerm) {
            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const historyList = document.getElementById('history-list');

            if (!searchTerm.trim()) {
                // 如果搜索词为空，显示所有历史
                loadChatHistory();
                return;
            }

            // 过滤匹配的会话
            const filteredSessions = sessions.filter(session =>
                session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (session.messages && session.messages.some(msg =>
                    msg.content.toLowerCase().includes(searchTerm.toLowerCase())
                ))
            );

            if (filteredSessions.length === 0) {
                historyList.innerHTML = '<div class="no-history">未找到匹配的对话<br><small>尝试其他关键词</small></div>';
                return;
            }

            // 按更新时间排序
            const sortedSessions = filteredSessions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

            historyList.innerHTML = sortedSessions.map(session => {
                const date = new Date(session.updatedAt);
                const now = new Date();
                const diffTime = Math.abs(now - date);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                let timeDisplay;
                if (diffDays === 1) {
                    timeDisplay = '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays === 2) {
                    timeDisplay = '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays <= 7) {
                    timeDisplay = diffDays + '天前';
                } else {
                    timeDisplay = date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
                }

                // 高亮搜索词
                const highlightedTitle = session.title.replace(
                    new RegExp(searchTerm, 'gi'),
                    `<mark style="background: rgba(255, 255, 0, 0.3); color: white;">$&</mark>`
                );

                const title = highlightedTitle.length > 50 ? highlightedTitle.substring(0, 50) + '...' : highlightedTitle;

                return `
                    <div class="history-item" title="${session.title}">
                        <div class="history-item-content" onclick="loadSession('${session.id}')">
                            <div class="history-item-title">${title}</div>
                            <div class="history-item-time">${timeDisplay}</div>
                        </div>
                        <div class="history-item-actions">
                            <button class="history-delete-btn" onclick="deleteSession('${session.id}', event)" title="删除对话">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 🗑️ 删除会话
        function deleteSession(sessionId, event) {
            // 阻止事件冒泡，避免触发loadSession
            event.stopPropagation();

            if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
                return;
            }

            console.log('🗑️ Deleting session:', sessionId);

            // 从localStorage删除会话
            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const updatedSessions = sessions.filter(s => s.id !== sessionId);
            localStorage.setItem('chatSessions', JSON.stringify(updatedSessions));

            // 如果删除的是当前会话，清空聊天
            if (currentSessionId === sessionId) {
                chatMessages = [];
                updateChatDisplay();
                currentSessionId = generateSessionId();
                console.log('🔄 Current session deleted, started new session:', currentSessionId);
            }

            // 重新加载历史列表
            const searchInput = document.getElementById('history-search-input');
            if (searchInput && searchInput.value.trim()) {
                searchChatHistory(searchInput.value);
            } else {
                loadChatHistory();
            }

            console.log('✅ Session deleted successfully');
        }

        // 🎨 用户体验优化函数

        // 显示加载覆盖层
        function showLoading(text = '正在处理中...') {
            const overlay = document.getElementById('loading-overlay');
            const loadingText = document.getElementById('loading-text');
            loadingText.textContent = text;
            overlay.classList.add('show');
        }

        // 隐藏加载覆盖层
        function hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.classList.remove('show');
        }

        // 显示错误提示
        function showErrorToast(message, duration = 5000) {
            const toast = document.getElementById('error-toast');
            const content = document.getElementById('error-content');
            content.textContent = message;
            toast.classList.add('show');

            // 自动隐藏
            setTimeout(() => {
                hideErrorToast();
            }, duration);
        }

        // 隐藏错误提示
        function hideErrorToast() {
            const toast = document.getElementById('error-toast');
            toast.classList.remove('show');
        }

        // 显示成功提示
        function showSuccessToast(message, duration = 3000) {
            const toast = document.getElementById('success-toast');
            const content = document.getElementById('success-content');
            content.textContent = message;
            toast.classList.add('show');

            // 自动隐藏
            setTimeout(() => {
                hideSuccessToast();
            }, duration);
        }

        // 隐藏成功提示
        function hideSuccessToast() {
            const toast = document.getElementById('success-toast');
            toast.classList.remove('show');
        }

        // 重复的简单版本已删除，使用完整的分步骤版本

        // 添加消息状态指示器
        function addMessageStatus(messageElement, status) {
            const existingStatus = messageElement.querySelector('.message-status');
            if (existingStatus) {
                existingStatus.remove();
            }

            const statusElement = document.createElement('span');
            statusElement.className = `message-status status-${status}`;

            switch (status) {
                case 'sending':
                    statusElement.innerHTML = '<i class="bi bi-clock"></i> 发送中';
                    break;
                case 'sent':
                    statusElement.innerHTML = '<i class="bi bi-check"></i> 已发送';
                    break;
                case 'error':
                    statusElement.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 发送失败';
                    break;
            }

            messageElement.appendChild(statusElement);
        }

        // 聊天历史功能（保持兼容性）
        function showChatHistory() {
            toggleHistoryPanel();
        }

        // 保存当前会话到历史
        function saveCurrentSession() {
            if (!currentSessionId || chatMessages.length === 0) return;

            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const sessionIndex = sessions.findIndex(s => s.id === currentSessionId);

            const sessionData = {
                id: currentSessionId,
                title: currentSessionTitle,
                messages: chatMessages,
                updatedAt: new Date().toISOString()
            };

            if (sessionIndex >= 0) {
                sessions[sessionIndex] = sessionData;
            } else {
                sessions.unshift(sessionData);
            }

            // 限制历史记录数量
            if (sessions.length > 50) {
                sessions.splice(50);
            }

            localStorage.setItem('chatSessions', JSON.stringify(sessions));
        }


    </script>

    <!-- 添加输入指示器的CSS样式 -->
    <style>
        .typing-animation {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-animation span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #6c757d;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-animation span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-animation span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 🔧 新增：发送按钮样式 */
        .send-button {
            position: absolute;
            right: 12px;
            bottom: 12px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            z-index: 1000; /* 确保按钮在最上层 */
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .send-button:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .send-button.loading {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 🔧 新增：停止按钮样式 */
        .stop-button {
            position: absolute;
            right: 12px;
            bottom: 12px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
            z-index: 1001;
            animation: stopPulse 2s infinite;
        }

        .stop-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        @keyframes stopPulse {
            0% { box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3); }
            50% { box-shadow: 0 4px 16px rgba(255, 107, 107, 0.6); }
            100% { box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3); }
        }

        /* 加载旋转器 */
        .loading-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 输入框容器相对定位 */
        .input-wrapper {
            position: relative;
        }

        /* 等待状态的输入框样式 */
        #chat-input:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 🔧 新增：增强的等待提示样式 */
        .typing-message {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #667eea;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .typing-status {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 8px 0;
        }

        .typing-text {
            font-size: 15px;
            color: #4a90e2;
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        .typing-progress {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 140px;
            height: 6px;
            background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4a90e2 0%, #357abd 50%, #4a90e2 100%);
            border-radius: 8px;
            animation: progressFlow 2.5s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: progressShine 1.5s infinite;
        }

        @keyframes progressFlow {
            0% { width: 0%; }
            50% { width: 75%; }
            100% { width: 100%; }
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 13px;
            color: #4a90e2;
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        /* 🎨 精美步骤进度样式 */
        .typing-steps {
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
            padding: 12px 8px;
            background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .typing-steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(74,144,226,0.1) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            flex: 1;
            position: relative;
            opacity: 0.3;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
        }

        .step.active {
            opacity: 1;
            transform: scale(1.05) translateY(-2px);
        }

        .step.completed {
            opacity: 0.9;
            transform: scale(0.95);
        }

        .step.completed .step-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 50%;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .step.active .step-icon {
            animation: stepPulse 1.8s infinite ease-in-out;
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            border-radius: 50%;
            padding: 4px;
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
        }

        @keyframes stepPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
            }
            50% {
                transform: scale(1.15);
                box-shadow: 0 6px 20px rgba(74, 144, 226, 0.6);
            }
        }

        .step-icon {
            font-size: 18px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .step-text {
            font-size: 11px;
            color: #8e9aaf;
            font-weight: 600;
            text-align: center;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .step.active .step-text {
            color: #4a90e2;
            font-weight: 700;
        }

        .step.completed .step-text {
            color: #28a745;
        }

        .step.completed .step-text {
            color: #28a745;
        }

        /* 步骤连接线 */
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 12px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: -1;
        }

        .step.completed:not(:last-child)::after {
            background: #28a745;
        }

        /* 🔧 新增：消息操作按钮样式 */
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            color: #6c757d;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f8f9fa;
            color: #495057;
            border-color: #adb5bd;
        }

        .action-btn.edit-btn:hover {
            background: #e3f2fd;
            color: #1976d2;
            border-color: #1976d2;
        }

        .action-btn.resend-btn:hover {
            background: #f3e5f5;
            color: #7b1fa2;
            border-color: #7b1fa2;
        }

        .action-btn.copy-btn:hover {
            background: #e8f5e8;
            color: #2e7d32;
            border-color: #2e7d32;
        }

        .action-btn.regenerate-btn:hover {
            background: #fff3e0;
            color: #f57c00;
            border-color: #f57c00;
        }

        /* 编辑界面样式 */
        .edit-container {
            margin-top: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .edit-textarea {
            width: 100%;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            min-height: 60px;
        }

        .edit-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .edit-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            justify-content: flex-end;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* Toast通知样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-success {
            background: #28a745;
        }

        .toast-error {
            background: #dc3545;
        }

        .toast-warning {
            background: #ffc107;
            color: #212529;
        }

        .toast-info {
            background: #17a2b8;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }

        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>













        function toggleModelSelector() {
            const dropdown = document.getElementById('model-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            }
        }

        async function switchModel(modelName) {
            try {
                const response = await fetch('/api/v1/models/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ model_name: modelName })
                });

                const result = await response.json();

                if (result.success) {
                    currentModel = result.data;
                    updateModelSelector();

                    // 更新下拉菜单中的当前状态
                    availableModels.forEach(model => {
                        model.is_current = model.name === modelName;
                    });
                    updateModelDropdown();

                    // 隐藏下拉菜单
                    toggleModelSelector();

                    console.log('Model switched to:', currentModel.display_name);
                } else {
                    console.error('Failed to switch model:', result.error);
                    alert('切换模型失败: ' + result.error);
                }
            } catch (error) {
                console.error('Error switching model:', error);
                alert('切换模型时发生错误');
            }
        }

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            const modelSelector = document.querySelector('.model-selector');
            const dropdown = document.getElementById('model-dropdown');

            if (dropdown && !modelSelector.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

        // 主机管理相关变量
        let hostsList = [];
        let filteredHosts = [];

        // 主机管理功能
        async function loadHostsList() {
            const loadingElement = document.getElementById('hosts-loading');
            const gridElement = document.getElementById('hosts-grid');
            const emptyElement = document.getElementById('hosts-empty');

            // 显示加载状态
            if (loadingElement) loadingElement.style.display = 'flex';
            if (gridElement) gridElement.style.display = 'none';
            if (emptyElement) emptyElement.style.display = 'none';

            try {
                // 使用优化的fetch函数，带缓存和重复请求防护
                const result = await optimizedFetch('/api/v1/hosts', {}, 'hosts_list', 60000); // 1分钟缓存

                if (result.code === 200 && result.data) {
                    hostsList = result.data.hosts || [];
                    filteredHosts = [...hostsList];
                    renderHostsList();
                } else {
                    throw new Error(result.message || '获取主机列表失败');
                }
            } catch (error) {
                console.error('Error loading hosts:', error);
                showNotification('加载主机列表失败: ' + error.message, 'error');

                // 显示空状态
                if (loadingElement) loadingElement.style.display = 'none';
                if (emptyElement) emptyElement.style.display = 'flex';
            }
        }

        function renderHostsList() {
            const loadingElement = document.getElementById('hosts-loading');
            const gridElement = document.getElementById('hosts-grid');
            const emptyElement = document.getElementById('hosts-empty');

            // 隐藏加载状态
            if (loadingElement) loadingElement.style.display = 'none';

            if (filteredHosts.length === 0) {
                if (gridElement) gridElement.style.display = 'none';
                if (emptyElement) emptyElement.style.display = 'flex';
                return;
            }

            // 显示主机网格
            if (gridElement) gridElement.style.display = 'grid';
            if (emptyElement) emptyElement.style.display = 'none';

            // 生成主机卡片HTML
            const hostsHTML = filteredHosts.map(host => createHostCard(host)).join('');
            if (gridElement) gridElement.innerHTML = hostsHTML;
        }

        function createHostCard(host) {
            const statusClass = host.status || 'unknown';
            const statusText = {
                'online': '在线',
                'offline': '离线',
                'unknown': '未知'
            }[statusClass] || '未知';

            return `
                <div class="host-card" data-host-id="${host.id}">
                    <div class="host-card-header">
                        <div class="host-select">
                            <input type="checkbox" class="host-checkbox" data-host-id="${host.id}" onchange="updateBatchActions()">
                        </div>
                        <h3 class="host-name">${escapeHtml(host.name)}</h3>
                        <span class="host-status ${statusClass}">${statusText}</span>
                    </div>
                    <div class="host-info">
                        <div class="host-info-item">
                            <i class="bi bi-hdd-network"></i>
                            <span>${escapeHtml(host.ip_address)}:${host.port || 22}</span>
                        </div>
                        <div class="host-info-item">
                            <i class="bi bi-person"></i>
                            <span>${escapeHtml(host.username)}</span>
                        </div>
                        <div class="host-info-item">
                            <i class="bi bi-tag"></i>
                            <span>${escapeHtml(host.environment || 'production')}</span>
                        </div>
                        ${host.latency ? `
                        <div class="host-info-item">
                            <i class="bi bi-speedometer2"></i>
                            <span>延迟: ${host.latency}ms</span>
                        </div>
                        ` : ''}
                        ${host.last_check ? `
                        <div class="host-info-item">
                            <i class="bi bi-clock"></i>
                            <span>最后检查: ${new Date(host.last_check).toLocaleTimeString()}</span>
                        </div>
                        ` : ''}
                        ${host.description ? `
                        <div class="host-info-item">
                            <i class="bi bi-info-circle"></i>
                            <span>${escapeHtml(host.description)}</span>
                        </div>
                        ` : ''}
                    </div>
                    <div class="host-actions">
                        <button class="host-action-btn primary" onclick="showHostDetails(${host.id})">
                            <i class="bi bi-info-circle"></i> 详情
                        </button>
                        <button class="host-action-btn" onclick="testHostConnection(${host.id})">
                            <i class="bi bi-wifi"></i> 测试连接
                        </button>
                        <button class="host-action-btn" onclick="editHost(${host.id})">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="host-action-btn danger" onclick="deleteHost(${host.id})">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 优化的主机过滤函数（带防抖）
        function filterHosts() {
            debounce('host_filter', () => {
                const searchTerm = document.getElementById('host-search')?.value.toLowerCase() || '';
                const statusFilter = document.getElementById('status-filter')?.value || '';
                const environmentFilter = document.getElementById('environment-filter')?.value || '';

                filteredHosts = hostsList.filter(host => {
                    const matchesSearch = !searchTerm ||
                        host.name.toLowerCase().includes(searchTerm) ||
                        host.ip_address.toLowerCase().includes(searchTerm) ||
                        (host.description && host.description.toLowerCase().includes(searchTerm));

                    const matchesStatus = !statusFilter || host.status === statusFilter;
                    const matchesEnvironment = !environmentFilter || host.environment === environmentFilter;

                    return matchesSearch && matchesStatus && matchesEnvironment;
                });

                renderHostsList();
            }, 300); // 300ms防抖延迟
        }

        function refreshHostList() {
            loadHostsList();
        }

        // 测试主机连接
        async function testHostConnection(hostId) {
            const hostCard = document.querySelector(`[data-host-id="${hostId}"]`);
            if (!hostCard) return;

            const testBtn = hostCard.querySelector('.host-action-btn.primary');
            const originalText = testBtn.innerHTML;

            // 显示测试中状态
            testBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 测试中...';
            testBtn.disabled = true;

            try {
                const response = await fetch(`/api/v1/hosts/${hostId}/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.code === 200 && result.data) {
                    const success = result.data.success;
                    const message = result.data.message || (success ? '连接成功' : '连接失败');

                    showNotification(message, success ? 'success' : 'error');

                    // 更新主机状态
                    const statusElement = hostCard.querySelector('.host-status');
                    if (statusElement) {
                        statusElement.className = `host-status ${success ? 'online' : 'offline'}`;
                        statusElement.textContent = success ? '在线' : '离线';
                    }
                } else {
                    throw new Error(result.message || '连接测试失败');
                }
            } catch (error) {
                console.error('Error testing connection:', error);
                showNotification('连接测试失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            }
        }

        // 显示添加主机模态框
        function showAddHostModal() {
            const modalHTML = `
                <div class="modal-overlay" id="add-host-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">添加主机</h3>
                            <button class="modal-close" onclick="closeAddHostModal()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="add-host-form">
                                <div class="form-group">
                                    <label class="form-label">主机名 *</label>
                                    <input type="text" class="form-input" id="host-name" name="name" required>
                                    <div class="form-error" id="name-error"></div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">IP地址 *</label>
                                        <input type="text" class="form-input" id="host-ip" name="ip_address" required>
                                        <div class="form-error" id="ip-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">端口</label>
                                        <input type="number" class="form-input" id="host-port" name="port" value="22" min="1" max="65535">
                                        <div class="form-error" id="port-error"></div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">用户名 *</label>
                                        <input type="text" class="form-input" id="host-username" name="username" required>
                                        <div class="form-error" id="username-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">密码</label>
                                        <input type="password" class="form-input" id="host-password" name="password">
                                        <div class="form-error" id="password-error"></div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">环境</label>
                                    <select class="form-input" id="host-environment" name="environment">
                                        <option value="production">生产环境</option>
                                        <option value="staging">测试环境</option>
                                        <option value="development">开发环境</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">描述</label>
                                    <input type="text" class="form-input" id="host-description" name="description" placeholder="可选的主机描述信息">
                                </div>

                                <div id="connection-test-result"></div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeAddHostModal()">取消</button>
                            <button type="button" class="btn btn-secondary" onclick="testConnectionInModal()">测试连接</button>
                            <button type="button" class="btn btn-primary" onclick="submitAddHost()">添加主机</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 显示模态框
            setTimeout(() => {
                const modal = document.getElementById('add-host-modal');
                if (modal) modal.classList.add('show');
            }, 10);
        }

        function closeAddHostModal() {
            const modal = document.getElementById('add-host-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // 在模态框中测试连接
        async function testConnectionInModal() {
            const form = document.getElementById('add-host-form');
            const resultDiv = document.getElementById('connection-test-result');

            // 获取表单数据
            const formData = new FormData(form);
            const hostData = {
                ip_address: formData.get('ip_address'),
                port: parseInt(formData.get('port')) || 22,
                username: formData.get('username'),
                password: formData.get('password')
            };

            // 验证必填字段
            if (!hostData.ip_address || !hostData.username) {
                resultDiv.innerHTML = '<div class="connection-test error">请填写IP地址和用户名</div>';
                return;
            }

            // 显示测试中状态
            resultDiv.innerHTML = '<div class="connection-test testing">正在测试连接...</div>';

            try {
                // 这里我们创建一个临时主机进行测试
                const response = await fetch('/api/v1/hosts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ...hostData,
                        name: 'temp_test_' + Date.now(),
                        description: 'Temporary host for connection testing'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.code === 201 && result.data) {
                        // 测试连接
                        const testResponse = await fetch(`/api/v1/hosts/${result.data.id}/test`, {
                            method: 'POST'
                        });

                        const testResult = await testResponse.json();

                        // 删除临时主机
                        await fetch(`/api/v1/hosts/${result.data.id}`, {
                            method: 'DELETE'
                        });

                        if (testResult.code === 200 && testResult.data) {
                            const success = testResult.data.success;
                            const message = testResult.data.message || (success ? '连接成功' : '连接失败');
                            resultDiv.innerHTML = `<div class="connection-test ${success ? 'success' : 'error'}">${message}</div>`;
                        } else {
                            throw new Error(testResult.message || '连接测试失败');
                        }
                    } else {
                        throw new Error(result.message || '创建临时主机失败');
                    }
                } else {
                    throw new Error('网络请求失败');
                }
            } catch (error) {
                console.error('Error testing connection:', error);
                resultDiv.innerHTML = `<div class="connection-test error">连接测试失败: ${error.message}</div>`;
            }
        }

        // 显示添加主机对话框
        function showAddHostModal() {
            const modal = new bootstrap.Modal(document.getElementById('addHostModal'));
            modal.show();
        }

        // 提交添加主机
        async function submitAddHost() {
            const form = document.getElementById('addHostForm');
            const formData = new FormData(form);

            const hostData = {
                name: formData.get('name'),
                ip_address: formData.get('ip_address'),
                port: parseInt(formData.get('port')) || 22,
                username: formData.get('username'),
                password: formData.get('password'),
                ssh_key_path: formData.get('ssh_key_path'),
                ssh_key_passphrase: formData.get('ssh_key_passphrase'),
                environment: formData.get('environment') || 'production',
                group_name: formData.get('group_name'),
                description: formData.get('description'),
                monitoring_enabled: formData.get('monitoring_enabled') === 'on',
                backup_enabled: formData.get('backup_enabled') === 'on'
            };

            // 基本验证
            if (!hostData.name) {
                showNotification('请输入主机名', 'error');
                return;
            }
            if (!hostData.ip_address) {
                showNotification('请输入IP地址', 'error');
                return;
            }
            if (!hostData.username) {
                showNotification('请输入用户名', 'error');
                return;
            }
            if (!hostData.password && !hostData.ssh_key_path) {
                showNotification('请至少提供密码或SSH密钥路径中的一种认证方式', 'error');
                return;
            }
            try {
                // 显示加载状态
                const submitBtn = document.querySelector('#addHostModal .btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 添加中...';
                submitBtn.disabled = true;

                const response = await fetch('/api/v1/hosts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(hostData)
                });

                const result = await response.json();

                if (result.code === 201) {
                    showNotification('主机添加成功', 'success');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addHostModal'));
                    modal.hide();
                    form.reset();
                    loadHostsList();
                } else {
                    throw new Error(result.message || '添加主机失败');
                }
            } catch (error) {
                console.error('Error adding host:', error);
                showNotification('添加主机失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                const submitBtn = document.querySelector('#addHostModal .btn-primary');
                submitBtn.innerHTML = '<i class="bi bi-plus-circle"></i> 添加主机';
                submitBtn.disabled = false;
            }
        }

        // 编辑主机
        function editHost(hostId) {
            const host = hostsList.find(h => h.id === hostId);
            if (!host) {
                showNotification('主机不存在', 'error');
                return;
            }

            // 填充编辑表单
            document.getElementById('editHostId').value = host.id;
            document.getElementById('editHostName').value = host.name;
            document.getElementById('editHostIP').value = host.ip_address;
            document.getElementById('editHostPort').value = host.port;
            document.getElementById('editHostUsername').value = host.username;
            document.getElementById('editHostEnvironment').value = host.environment;
            document.getElementById('editHostDescription').value = host.description || '';
            document.getElementById('editHostMonitoring').checked = host.monitoring_enabled;
            document.getElementById('editHostBackup').checked = host.backup_enabled;

            // 显示编辑对话框
            const modal = new bootstrap.Modal(document.getElementById('editHostModal'));
            modal.show();
        }

        // 提交编辑主机
        async function submitEditHost() {
            const form = document.getElementById('editHostForm');
            const formData = new FormData(form);
            const hostId = formData.get('id');

            const hostData = {
                name: formData.get('name'),
                ip_address: formData.get('ip_address'),
                port: parseInt(formData.get('port')) || 22,
                username: formData.get('username'),
                environment: formData.get('environment') || 'production',
                description: formData.get('description'),
                monitoring_enabled: formData.get('monitoring_enabled') === 'on',
                backup_enabled: formData.get('backup_enabled') === 'on'
            };

            // 如果提供了新密码，则包含在更新数据中
            const password = formData.get('password');
            if (password) {
                hostData.password = password;
            }

            // 基本验证
            if (!hostData.name) {
                showNotification('请输入主机名', 'error');
                return;
            }
            if (!hostData.ip_address) {
                showNotification('请输入IP地址', 'error');
                return;
            }
            if (!hostData.username) {
                showNotification('请输入用户名', 'error');
                return;
            }

            try {
                // 显示加载状态
                const submitBtn = document.querySelector('#editHostModal .btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
                submitBtn.disabled = true;

                const response = await fetch(`/api/v1/hosts/${hostId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(hostData)
                });

                const result = await response.json();

                if (result.code === 200) {
                    showNotification('主机更新成功', 'success');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editHostModal'));
                    modal.hide();
                    loadHostsList();
                } else {
                    throw new Error(result.message || '更新主机失败');
                }
            } catch (error) {
                console.error('Error updating host:', error);
                showNotification('更新主机失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                const submitBtn = document.querySelector('#editHostModal .btn-primary');
                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 保存修改';
                submitBtn.disabled = false;
            }
        }

        // 测试主机连接
        async function testHostConnection(hostId) {
            try {
                showNotification('正在测试连接...', 'info');

                const response = await fetch(`/api/v1/hosts/${hostId}/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.code === 200) {
                    const testResult = result.data;
                    if (testResult.success) {
                        showNotification(`连接测试成功 (${testResult.duration}ms)`, 'success');
                    } else {
                        showNotification(`连接测试失败: ${testResult.message}`, 'warning');
                    }
                    loadHostsList(); // 刷新列表以更新状态
                } else {
                    throw new Error(result.message || '测试连接失败');
                }
            } catch (error) {
                console.error('Error testing connection:', error);
                showNotification('测试连接失败: ' + error.message, 'error');
            }
        }

        // 删除主机
        async function deleteHost(hostId) {
            const host = hostsList.find(h => h.id === hostId);
            if (!host) return;

            if (!confirm(`确定要删除主机 "${host.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/v1/hosts/${hostId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.code === 200) {
                    showNotification('主机删除成功', 'success');
                    refreshHostList();
                } else {
                    throw new Error(result.message || '删除主机失败');
                }
            } catch (error) {
                console.error('Error deleting host:', error);
                showNotification('删除主机失败: ' + error.message, 'error');
            }
        }

        // 当切换到主机视图时加载数据
        function switchToHosts() {
            switchView('hosts');
            // 如果还没有加载过主机列表，则加载
            if (hostsList.length === 0) {
                loadHostsList();
            }
            // 启动主机状态监控
            startHostMonitoring();
        }

        // 批量操作相关函数
        let selectedHosts = new Set();

        // 更新批量操作按钮状态
        function updateBatchActions() {
            const checkboxes = document.querySelectorAll('.host-checkbox:checked');
            const batchActions = document.getElementById('batch-actions');
            const batchCount = document.getElementById('batch-count');

            selectedHosts.clear();
            checkboxes.forEach(checkbox => {
                selectedHosts.add(parseInt(checkbox.dataset.hostId));
            });

            if (selectedHosts.size > 0) {
                batchActions.style.display = 'flex';
                batchCount.textContent = `已选择 ${selectedHosts.size} 项`;
            } else {
                batchActions.style.display = 'none';
            }
        }

        // 清除选择
        function clearSelection() {
            document.querySelectorAll('.host-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            selectedHosts.clear();
            updateBatchActions();
        }

        // 批量测试连接
        async function batchTestConnection() {
            if (selectedHosts.size === 0) {
                showNotification('请先选择要测试的主机', 'warning');
                return;
            }

            const hostIds = Array.from(selectedHosts);
            showNotification(`正在测试 ${hostIds.length} 台主机的连接...`, 'info');

            let successCount = 0;
            let failCount = 0;

            for (const hostId of hostIds) {
                try {
                    const response = await fetch(`/api/v1/hosts/${hostId}/test`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const result = await response.json();
                    if (result.code === 200 && result.data.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }

            showNotification(`批量测试完成：成功 ${successCount} 台，失败 ${failCount} 台`,
                failCount === 0 ? 'success' : 'warning');

            // 刷新主机列表以更新状态
            loadHostsList();
        }

        // 批量删除主机
        async function batchDeleteHosts() {
            if (selectedHosts.size === 0) {
                showNotification('请先选择要删除的主机', 'warning');
                return;
            }

            const hostIds = Array.from(selectedHosts);
            const hostNames = hostIds.map(id => {
                const host = hostsList.find(h => h.id === id);
                return host ? host.name : `ID:${id}`;
            }).join(', ');

            if (!confirm(`确定要删除以下 ${hostIds.length} 台主机吗？\n\n${hostNames}\n\n此操作不可撤销！`)) {
                return;
            }

            showNotification(`正在删除 ${hostIds.length} 台主机...`, 'info');

            let successCount = 0;
            let failCount = 0;

            for (const hostId of hostIds) {
                try {
                    const response = await fetch(`/api/v1/hosts/${hostId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const result = await response.json();
                    if (result.code === 200) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }

            showNotification(`批量删除完成：成功 ${successCount} 台，失败 ${failCount} 台`,
                failCount === 0 ? 'success' : 'warning');

            // 清除选择并刷新列表
            clearSelection();
            loadHostsList();
        }

        // 主机实时状态监控
        let hostMonitoringInterval = null;
        let hostStatusWebSocket = null;

        // 启动主机状态监控
        function startHostMonitoring() {
            // 定期检查主机状态（每30秒）
            if (hostMonitoringInterval) {
                clearInterval(hostMonitoringInterval);
            }

            hostMonitoringInterval = setInterval(() => {
                if (currentView === 'hosts' && hostsList.length > 0) {
                    checkHostsStatus();
                }
            }, 30000); // 30秒检查一次

            // 尝试建立WebSocket连接进行实时更新
            initHostStatusWebSocket();
        }

        // 停止主机状态监控
        function stopHostMonitoring() {
            if (hostMonitoringInterval) {
                clearInterval(hostMonitoringInterval);
                hostMonitoringInterval = null;
            }

            if (hostStatusWebSocket) {
                hostStatusWebSocket.close();
                hostStatusWebSocket = null;
            }
        }

        // 初始化主机状态WebSocket连接
        function initHostStatusWebSocket() {
            if (hostStatusWebSocket) {
                hostStatusWebSocket.close();
            }

            try {
                const wsUrl = `${CONFIG.WS_BASE_URL}/ws/host-status`;
                hostStatusWebSocket = new WebSocket(wsUrl);

                hostStatusWebSocket.onopen = function() {
                    console.log('主机状态WebSocket连接已建立');
                };

                hostStatusWebSocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'host_status_update') {
                            updateHostStatus(data.host_id, data.status, data.latency);
                        }
                    } catch (error) {
                        console.error('解析主机状态WebSocket消息失败:', error);
                    }
                };

                hostStatusWebSocket.onclose = function() {
                    console.log('主机状态WebSocket连接已关闭');
                    // 5秒后尝试重连
                    setTimeout(() => {
                        if (currentView === 'hosts') {
                            initHostStatusWebSocket();
                        }
                    }, 5000);
                };

                hostStatusWebSocket.onerror = function(error) {
                    console.error('主机状态WebSocket连接错误:', error);
                };
            } catch (error) {
                console.error('创建主机状态WebSocket连接失败:', error);
            }
        }

        // 检查所有主机状态
        async function checkHostsStatus() {
            const onlineHosts = hostsList.filter(host => host.status === 'online');

            for (const host of onlineHosts) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(`/api/v1/hosts/${host.id}/test`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const result = await response.json();
                    const latency = Date.now() - startTime;

                    if (result.code === 200) {
                        const newStatus = result.data.success ? 'online' : 'offline';
                        updateHostStatus(host.id, newStatus, latency);
                    }
                } catch (error) {
                    // 连接失败，标记为离线
                    updateHostStatus(host.id, 'offline', 0);
                }
            }
        }

        // 更新单个主机状态
        function updateHostStatus(hostId, status, latency = 0) {
            // 更新内存中的主机列表
            const hostIndex = hostsList.findIndex(h => h.id === hostId);
            if (hostIndex !== -1) {
                const oldStatus = hostsList[hostIndex].status;
                hostsList[hostIndex].status = status;
                hostsList[hostIndex].latency = latency;
                hostsList[hostIndex].last_check = new Date();

                // 如果状态发生变化，显示通知
                if (oldStatus !== status) {
                    const host = hostsList[hostIndex];
                    const statusText = {
                        'online': '在线',
                        'offline': '离线',
                        'unknown': '未知'
                    }[status] || '未知';

                    showNotification(`主机 ${host.name} 状态变更为: ${statusText}`,
                        status === 'online' ? 'success' : 'warning');
                }

                // 更新过滤后的列表
                filterHosts();

                // 重新渲染主机卡片
                renderHostsList();
            }
        }

        // 主机详情相关函数
        let currentDetailHostId = null;

        // 显示主机详情
        function showHostDetails(hostId) {
            const host = hostsList.find(h => h.id === hostId);
            if (!host) {
                showNotification('主机不存在', 'error');
                return;
            }

            currentDetailHostId = hostId;

            // 填充基本信息
            document.getElementById('detail-host-name').textContent = host.name;
            document.getElementById('detail-host-ip').textContent = host.ip_address;
            document.getElementById('detail-host-port').textContent = host.port || 22;
            document.getElementById('detail-host-username').textContent = host.username;
            document.getElementById('detail-host-environment').textContent = host.environment || 'production';
            document.getElementById('detail-host-description').textContent = host.description || '无描述';

            // 状态显示
            const statusElement = document.getElementById('detail-host-status');
            const statusClass = host.status === 'online' ? 'bg-success' :
                               host.status === 'offline' ? 'bg-danger' : 'bg-secondary';
            const statusText = {
                'online': '在线',
                'offline': '离线',
                'unknown': '未知'
            }[host.status] || '未知';
            statusElement.className = `badge ${statusClass}`;
            statusElement.textContent = statusText;

            // 连接信息
            document.getElementById('detail-host-latency').textContent =
                host.latency ? `${host.latency}ms` : '未测试';
            document.getElementById('detail-host-last-check').textContent =
                host.last_check ? new Date(host.last_check).toLocaleString() : '从未检查';
            document.getElementById('detail-host-created').textContent =
                host.created_at ? new Date(host.created_at).toLocaleString() : '未知';
            document.getElementById('detail-host-updated').textContent =
                host.updated_at ? new Date(host.updated_at).toLocaleString() : '未知';

            // 监控和备份状态
            const monitoringElement = document.getElementById('detail-host-monitoring');
            monitoringElement.className = `badge ${host.monitoring_enabled ? 'bg-success' : 'bg-secondary'}`;
            monitoringElement.textContent = host.monitoring_enabled ? '已启用' : '已禁用';

            const backupElement = document.getElementById('detail-host-backup');
            backupElement.className = `badge ${host.backup_enabled ? 'bg-success' : 'bg-secondary'}`;
            backupElement.textContent = host.backup_enabled ? '已启用' : '已禁用';

            // 重置系统信息和操作历史
            document.getElementById('system-info-content').innerHTML = `
                <div class="col-12 text-center">
                    <button class="btn btn-outline-primary" onclick="loadSystemInfo()">
                        <i class="bi bi-arrow-clockwise"></i> 获取系统信息
                    </button>
                </div>
            `;

            document.getElementById('operation-history-content').innerHTML = `
                <div class="text-center">
                    <button class="btn btn-outline-primary" onclick="loadOperationHistory()">
                        <i class="bi bi-arrow-clockwise"></i> 加载操作历史
                    </button>
                </div>
            `;

            // 显示对话框
            const modal = new bootstrap.Modal(document.getElementById('hostDetailsModal'));
            modal.show();
        }

        // 从详情页面编辑主机
        function editHostFromDetails() {
            if (currentDetailHostId) {
                // 关闭详情对话框
                const detailsModal = bootstrap.Modal.getInstance(document.getElementById('hostDetailsModal'));
                detailsModal.hide();

                // 打开编辑对话框
                setTimeout(() => {
                    editHost(currentDetailHostId);
                }, 300);
            }
        }

        // 加载系统信息
        async function loadSystemInfo() {
            if (!currentDetailHostId) return;

            const contentElement = document.getElementById('system-info-content');
            contentElement.innerHTML = `
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取系统信息...</p>
                </div>
            `;

            try {
                // 模拟系统信息获取（实际应该调用后端API）
                await new Promise(resolve => setTimeout(resolve, 1500));

                const systemInfo = {
                    os: 'Ubuntu 20.04.3 LTS',
                    kernel: '5.4.0-88-generic',
                    cpu: 'Intel(R) Xeon(R) CPU E5-2686 v4 @ 2.30GHz (4 cores)',
                    memory: '8.0 GB',
                    disk: '50 GB SSD',
                    uptime: '15 days, 3 hours, 42 minutes',
                    load_average: '0.15, 0.18, 0.12'
                };

                contentElement.innerHTML = `
                    <div class="col-md-6">
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>操作系统:</strong></div>
                            <div class="col-sm-8">${systemInfo.os}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>内核版本:</strong></div>
                            <div class="col-sm-8">${systemInfo.kernel}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>CPU:</strong></div>
                            <div class="col-sm-8">${systemInfo.cpu}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>内存:</strong></div>
                            <div class="col-sm-8">${systemInfo.memory}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>磁盘:</strong></div>
                            <div class="col-sm-8">${systemInfo.disk}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>运行时间:</strong></div>
                            <div class="col-sm-8">${systemInfo.uptime}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>负载平均:</strong></div>
                            <div class="col-sm-8">${systemInfo.load_average}</div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-sm btn-outline-secondary" onclick="loadSystemInfo()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                contentElement.innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>获取系统信息失败: ${error.message}</p>
                        <button class="btn btn-outline-primary" onclick="loadSystemInfo()">
                            <i class="bi bi-arrow-clockwise"></i> 重试
                        </button>
                    </div>
                `;
            }
        }

        // 加载操作历史
        async function loadOperationHistory() {
            if (!currentDetailHostId) return;

            const contentElement = document.getElementById('operation-history-content');
            contentElement.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载操作历史...</p>
                </div>
            `;

            try {
                // 模拟操作历史获取（实际应该调用后端API）
                await new Promise(resolve => setTimeout(resolve, 1000));

                const operations = [
                    {
                        id: 1,
                        type: 'connection_test',
                        description: '连接测试',
                        status: 'success',
                        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
                        duration: 156
                    },
                    {
                        id: 2,
                        type: 'command_execution',
                        description: '执行命令: ls -la',
                        status: 'success',
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
                        duration: 89
                    },
                    {
                        id: 3,
                        type: 'system_update',
                        description: '系统更新检查',
                        status: 'failed',
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
                        duration: 0,
                        error: '权限不足'
                    },
                    {
                        id: 4,
                        type: 'file_transfer',
                        description: '文件传输: config.txt',
                        status: 'success',
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2天前
                        duration: 234
                    }
                ];

                const operationsHTML = operations.map(op => {
                    const statusClass = op.status === 'success' ? 'text-success' :
                                       op.status === 'failed' ? 'text-danger' : 'text-warning';
                    const statusIcon = op.status === 'success' ? 'bi-check-circle' :
                                      op.status === 'failed' ? 'bi-x-circle' : 'bi-clock';

                    return `
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center">
                                    <i class="bi ${statusIcon} ${statusClass} me-2"></i>
                                    <strong>${op.description}</strong>
                                </div>
                                <small class="text-muted">
                                    ${op.timestamp.toLocaleString()}
                                    ${op.duration > 0 ? ` • 耗时 ${op.duration}ms` : ''}
                                    ${op.error ? ` • 错误: ${op.error}` : ''}
                                </small>
                            </div>
                            <span class="badge ${op.status === 'success' ? 'bg-success' :
                                                op.status === 'failed' ? 'bg-danger' : 'bg-warning'}">
                                ${op.status === 'success' ? '成功' :
                                  op.status === 'failed' ? '失败' : '进行中'}
                            </span>
                        </div>
                    `;
                }).join('');

                contentElement.innerHTML = `
                    <div class="operation-history">
                        ${operationsHTML}
                        <div class="text-center mt-3">
                            <button class="btn btn-sm btn-outline-secondary" onclick="loadOperationHistory()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                `;
            } catch (error) {
                contentElement.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>加载操作历史失败: ${error.message}</p>
                        <button class="btn btn-outline-primary" onclick="loadOperationHistory()">
                            <i class="bi bi-arrow-clockwise"></i> 重试
                        </button>
                    </div>
                `;
            }
        }

        // 通知系统
        function showNotification(message, type = 'info', duration = 5000) {
            const container = document.getElementById('notification-container');
            if (!container) return;

            const notificationId = 'notification-' + Date.now();
            const icons = {
                success: 'bi-check-circle',
                error: 'bi-x-circle',
                warning: 'bi-exclamation-triangle',
                info: 'bi-info-circle'
            };

            const notificationHTML = `
                <div class="notification ${type}" id="${notificationId}">
                    <div class="notification-content">
                        <i class="notification-icon bi ${icons[type] || icons.info}"></i>
                        <span class="notification-message">${escapeHtml(message)}</span>
                    </div>
                    <button class="notification-close" onclick="closeNotification('${notificationId}')">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', notificationHTML);

            // 自动关闭
            if (duration > 0) {
                setTimeout(() => {
                    closeNotification(notificationId);
                }, duration);
            }
        }

        function closeNotification(notificationId) {
            const notification = document.getElementById(notificationId);
            if (notification) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }

        // 监控相关变量
        let monitoringCharts = {};
        let monitoringData = {};
        let monitoringInterval = null;

        // 优化的监控功能（带节流和并发控制）
        async function loadMonitoringData() {
            // 使用节流防止频繁调用
            if (!throttle('monitoring_data', () => {}, 5000)) { // 5秒节流
                return;
            }

            try {
                // 并发获取所有监控数据，使用缓存
                const [overviewData, healthData, metricsData, hostsData] = await Promise.allSettled([
                    optimizedFetch('/api/v1/stats/overview', {}, 'stats_overview', 30000), // 30秒缓存
                    optimizedFetch('/api/v1/stats/health', {}, 'stats_health', 30000),
                    optimizedFetch('/api/v1/stats/metrics', {}, 'stats_metrics', 30000),
                    optimizedFetch('/api/v1/hosts', {}, 'hosts_monitoring', 60000) // 1分钟缓存
                ]);

                // 处理系统概览数据
                if (overviewData.status === 'fulfilled') {
                    updateSystemOverview(overviewData.value.data || {});
                }

                // 处理系统健康数据
                if (healthData.status === 'fulfilled') {
                    updateSystemHealth(healthData.value.data || {});
                }

                // 处理系统指标数据
                if (metricsData.status === 'fulfilled') {
                    updateSystemMetrics(metricsData.value.data || {});
                }

                // 处理主机列表数据
                if (hostsData.status === 'fulfilled') {
                    updateHostsTable(hostsData.value.data?.hosts || []);
                }

                // 更新最后更新时间
                const lastUpdateElement = document.getElementById('last-update');
                if (lastUpdateElement) {
                    lastUpdateElement.textContent = '最后更新: ' + new Date().toLocaleTimeString();
                }

                // 检查是否有失败的请求
                const failedRequests = [overviewData, healthData, metricsData, hostsData]
                    .filter(result => result.status === 'rejected');

                if (failedRequests.length > 0) {
                    console.warn(`${failedRequests.length} 个监控数据请求失败`);
                }

            } catch (error) {
                console.error('Error loading monitoring data:', error);
                showNotification('加载监控数据失败: ' + error.message, 'error');
            }
        }

        function updateSystemOverview(data) {
            // 更新主机状态统计
            const hostsOnline = data.hosts_online || 0;
            const hostsOffline = data.hosts_offline || 0;
            const hostsTotal = hostsOnline + hostsOffline;

            document.getElementById('hosts-online').textContent = hostsOnline;
            document.getElementById('hosts-offline').textContent = hostsOffline;
            document.getElementById('hosts-total').textContent = hostsTotal;

            // 更新主机状态进度条
            const onlinePercentage = hostsTotal > 0 ? (hostsOnline / hostsTotal) * 100 : 0;
            document.getElementById('hosts-progress').style.width = onlinePercentage + '%';

            // 更新主机状态指示器
            const hostsStatusElement = document.getElementById('hosts-status');
            if (hostsStatusElement) {
                if (hostsOffline === 0) {
                    hostsStatusElement.textContent = '正常';
                    hostsStatusElement.className = 'status-indicator';
                } else if (hostsOffline < hostsTotal / 2) {
                    hostsStatusElement.textContent = '警告';
                    hostsStatusElement.className = 'status-indicator warning';
                } else {
                    hostsStatusElement.textContent = '异常';
                    hostsStatusElement.className = 'status-indicator error';
                }
            }
        }

        function updateSystemHealth(data) {
            // 更新告警统计
            const criticalAlerts = data.critical_alerts || 0;
            const warningAlerts = data.warning_alerts || 0;
            const infoAlerts = data.info_alerts || 0;

            document.getElementById('critical-alerts').textContent = criticalAlerts;
            document.getElementById('warning-alerts').textContent = warningAlerts;
            document.getElementById('info-alerts').textContent = infoAlerts;

            // 更新告警状态指示器
            const alertsStatusElement = document.getElementById('alerts-status');
            if (alertsStatusElement) {
                if (criticalAlerts > 0) {
                    alertsStatusElement.textContent = '严重';
                    alertsStatusElement.className = 'status-indicator error';
                } else if (warningAlerts > 0) {
                    alertsStatusElement.textContent = '警告';
                    alertsStatusElement.className = 'status-indicator warning';
                } else {
                    alertsStatusElement.textContent = '正常';
                    alertsStatusElement.className = 'status-indicator';
                }
            }

            // 更新最近告警列表
            const recentAlertsElement = document.getElementById('recent-alerts');
            if (recentAlertsElement && data.recent_alerts && data.recent_alerts.length > 0) {
                const alertsHTML = data.recent_alerts.map(alert => `
                    <div class="alert-item-small ${alert.severity}">
                        <span class="alert-time">${new Date(alert.created_at).toLocaleTimeString()}</span>
                        <span class="alert-message">${escapeHtml(alert.message)}</span>
                    </div>
                `).join('');
                recentAlertsElement.innerHTML = alertsHTML;
            } else if (recentAlertsElement) {
                recentAlertsElement.innerHTML = '<div class="no-alerts">暂无告警</div>';
            }
        }

        function updateSystemMetrics(data) {
            // 更新CPU使用率
            const cpuUsage = data.cpu_usage || 0;
            document.getElementById('cpu-usage').textContent = cpuUsage.toFixed(1);
            document.getElementById('cpu-bar').style.width = cpuUsage + '%';

            // 更新内存使用率
            const memoryUsage = data.memory_usage || 0;
            document.getElementById('memory-usage').textContent = memoryUsage.toFixed(1);
            document.getElementById('memory-bar').style.width = memoryUsage + '%';

            // 更新磁盘使用率
            const diskUsage = data.disk_usage || 0;
            document.getElementById('disk-usage').textContent = diskUsage.toFixed(1);
            document.getElementById('disk-bar').style.width = diskUsage + '%';

            // 更新性能状态指示器
            const performanceStatusElement = document.getElementById('performance-status');
            if (performanceStatusElement) {
                const maxUsage = Math.max(cpuUsage, memoryUsage, diskUsage);
                if (maxUsage < 70) {
                    performanceStatusElement.textContent = '良好';
                    performanceStatusElement.className = 'status-indicator';
                } else if (maxUsage < 90) {
                    performanceStatusElement.textContent = '警告';
                    performanceStatusElement.className = 'status-indicator warning';
                } else {
                    performanceStatusElement.textContent = '异常';
                    performanceStatusElement.className = 'status-indicator error';
                }
            }

            // 更新图表数据
            updateChartData('cpu', cpuUsage);
            updateChartData('memory', memoryUsage);
            updateChartData('disk', diskUsage);
        }

        function updateHostsTable(hosts) {
            const tableElement = document.getElementById('hosts-table');
            if (!tableElement) return;

            if (hosts.length === 0) {
                tableElement.innerHTML = '<div class="table-loading">暂无主机数据</div>';
                return;
            }

            const tableHTML = `
                <table class="hosts-detail-table">
                    <thead>
                        <tr>
                            <th>主机名</th>
                            <th>IP地址</th>
                            <th>状态</th>
                            <th>CPU</th>
                            <th>内存</th>
                            <th>磁盘</th>
                            <th>最后连接</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${hosts.map(host => `
                            <tr>
                                <td>
                                    <div class="host-name-cell">
                                        <i class="bi bi-server"></i>
                                        ${escapeHtml(host.name)}
                                    </div>
                                </td>
                                <td>${escapeHtml(host.ip_address)}</td>
                                <td>
                                    <span class="status-badge ${host.status || 'unknown'}">
                                        ${getStatusText(host.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="metric-cell">
                                        <span>${(Math.random() * 100).toFixed(1)}%</span>
                                        <div class="mini-bar">
                                            <div class="mini-fill" style="width: ${Math.random() * 100}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="metric-cell">
                                        <span>${(Math.random() * 100).toFixed(1)}%</span>
                                        <div class="mini-bar">
                                            <div class="mini-fill" style="width: ${Math.random() * 100}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="metric-cell">
                                        <span>${(Math.random() * 100).toFixed(1)}%</span>
                                        <div class="mini-bar">
                                            <div class="mini-fill" style="width: ${Math.random() * 100}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="time-cell">
                                        ${host.last_connected ? new Date(host.last_connected).toLocaleString() : '从未连接'}
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            tableElement.innerHTML = tableHTML;
        }

        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'unknown': '未知'
            };
            return statusMap[status] || '未知';
        }

        // 图表相关功能
        function initializeCharts() {
            const chartConfigs = {
                cpu: { label: 'CPU使用率', color: '#3b82f6' },
                memory: { label: '内存使用率', color: '#10b981' },
                disk: { label: '磁盘使用率', color: '#f59e0b' }
            };

            Object.keys(chartConfigs).forEach(type => {
                const canvas = document.getElementById(type + '-chart');
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    const config = chartConfigs[type];

                    monitoringCharts[type] = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: [],
                            datasets: [{
                                label: config.label,
                                data: [],
                                borderColor: config.color,
                                backgroundColor: config.color + '20',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                },
                                x: {
                                    display: true,
                                    ticks: {
                                        maxTicksLimit: 10
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            elements: {
                                point: {
                                    radius: 3,
                                    hoverRadius: 5
                                }
                            }
                        }
                    });

                    // 初始化空数据
                    monitoringData[type] = [];
                }
            });
        }

        function updateChartData(type, value) {
            if (!monitoringCharts[type]) return;

            const chart = monitoringCharts[type];
            const now = new Date();
            const timeLabel = now.toLocaleTimeString();

            // 添加新数据点
            chart.data.labels.push(timeLabel);
            chart.data.datasets[0].data.push(value);

            // 保持最多50个数据点
            if (chart.data.labels.length > 50) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }

            chart.update('none');
        }

        function updateChartPeriod(type, period) {
            // 这里可以根据时间段重新加载历史数据
            console.log(`Updating ${type} chart for period: ${period}`);
            // TODO: 实现根据时间段加载历史数据的功能
        }

        function refreshMonitoringData() {
            loadMonitoringData();
        }

        function refreshHostDetails() {
            loadMonitoringData();
        }

        // 当切换到监控视图时初始化
        function switchToMonitoring() {
            switchView('monitoring');

            // 如果图表还没有初始化，则初始化
            if (Object.keys(monitoringCharts).length === 0) {
                setTimeout(() => {
                    initializeCharts();
                    loadMonitoringData();

                    // 启动定时刷新
                    startMonitoringUpdates();
                }, 100);
            } else {
                loadMonitoringData();
                // 确保定时器在运行
                startMonitoringUpdates();
            }
        }

        // 启动监控数据更新
        function startMonitoringUpdates() {
            // 清除现有定时器
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            // 启动新的定时器，每30秒刷新一次
            monitoringInterval = setInterval(() => {
                // 只有在监控视图激活时才更新数据
                if (currentView === 'monitoring') {
                    loadMonitoringData();
                }
            }, 30000);
        }

        // 停止监控数据更新
        function stopMonitoringUpdates() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
        }

        // 重写switchView函数以处理监控定时器
        const originalSwitchView = switchView;
        switchView = function(viewName) {
            // 如果离开监控视图，停止定时更新
            if (currentView === 'monitoring' && viewName !== 'monitoring') {
                stopMonitoringUpdates();
            }

            // 调用原始的switchView函数
            originalSwitchView(viewName);

            // 如果进入监控视图，启动定时更新
            if (viewName === 'monitoring') {
                startMonitoringUpdates();
            }
        };

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            stopMonitoringUpdates();
        });

        // 页面可见性变化时处理定时器
        document.addEventListener('visibilitychange', function() {
            if (currentView === 'monitoring') {
                if (document.hidden) {
                    stopMonitoringUpdates();
                } else {
                    startMonitoringUpdates();
                    loadMonitoringData(); // 立即刷新一次数据
                }
            }
        });

        // 模拟实时数据生成（用于演示）
        function generateMockMetrics() {
            return {
                cpu_usage: Math.random() * 100,
                memory_usage: Math.random() * 100,
                disk_usage: Math.random() * 100,
                hosts_online: Math.floor(Math.random() * 10) + 1,
                hosts_offline: Math.floor(Math.random() * 3),
                critical_alerts: Math.floor(Math.random() * 3),
                warning_alerts: Math.floor(Math.random() * 5),
                info_alerts: Math.floor(Math.random() * 10)
            };
        }

        // 如果API返回空数据，使用模拟数据
        function fallbackToMockData() {
            const mockData = generateMockMetrics();
            updateSystemOverview(mockData);
            updateSystemHealth(mockData);
            updateSystemMetrics(mockData);
        }

        // 告警管理相关变量
        let alertsList = [];
        let filteredAlerts = [];

        // 告警管理功能
        async function loadAlerts() {
            const loadingElement = document.getElementById('alerts-loading');
            const listElement = document.getElementById('alerts-list');
            const emptyElement = document.getElementById('alerts-empty');

            // 显示加载状态
            if (loadingElement) loadingElement.style.display = 'flex';
            if (listElement) listElement.style.display = 'none';
            if (emptyElement) emptyElement.style.display = 'none';

            try {
                const response = await fetch('/api/v1/alerts', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.code === 200 && result.data) {
                    alertsList = result.data.alerts || [];
                    filteredAlerts = [...alertsList];
                    renderAlertsList();
                    updateAlertsStats();
                } else {
                    // 如果没有数据，生成模拟数据用于演示
                    alertsList = generateMockAlerts();
                    filteredAlerts = [...alertsList];
                    renderAlertsList();
                    updateAlertsStats();
                }
            } catch (error) {
                console.error('Error loading alerts:', error);
                // 生成模拟数据用于演示
                alertsList = generateMockAlerts();
                filteredAlerts = [...alertsList];
                renderAlertsList();
                updateAlertsStats();
            }
        }

        function generateMockAlerts() {
            const severities = ['critical', 'warning', 'info'];
            const statuses = ['active', 'acknowledged', 'resolved'];
            const hosts = ['web-server-01', 'db-server-01', 'cache-server-01', 'api-server-01'];
            const messages = [
                'CPU使用率超过90%',
                '内存使用率过高',
                '磁盘空间不足',
                '网络连接异常',
                '服务响应时间过长',
                '数据库连接失败',
                '缓存服务不可用',
                'SSL证书即将过期'
            ];

            const alerts = [];
            for (let i = 0; i < 15; i++) {
                const severity = severities[Math.floor(Math.random() * severities.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const host = hosts[Math.floor(Math.random() * hosts.length)];
                const message = messages[Math.floor(Math.random() * messages.length)];

                alerts.push({
                    id: i + 1,
                    severity: severity,
                    status: status,
                    host: host,
                    message: message,
                    description: `${host}上检测到${message}，请及时处理。`,
                    created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
                    updated_at: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
                });
            }

            return alerts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        }

        function renderAlertsList() {
            const loadingElement = document.getElementById('alerts-loading');
            const listElement = document.getElementById('alerts-list');
            const emptyElement = document.getElementById('alerts-empty');

            // 隐藏加载状态
            if (loadingElement) loadingElement.style.display = 'none';

            if (filteredAlerts.length === 0) {
                if (listElement) listElement.style.display = 'none';
                if (emptyElement) emptyElement.style.display = 'flex';
                return;
            }

            // 显示告警列表
            if (listElement) listElement.style.display = 'flex';
            if (emptyElement) emptyElement.style.display = 'none';

            // 生成告警项目HTML
            const alertsHTML = filteredAlerts.map(alert => createAlertItem(alert)).join('');
            if (listElement) listElement.innerHTML = alertsHTML;
        }

        function createAlertItem(alert) {
            const createdTime = new Date(alert.created_at).toLocaleString();
            const updatedTime = new Date(alert.updated_at).toLocaleString();

            const severityText = {
                'critical': '严重',
                'warning': '警告',
                'info': '信息'
            }[alert.severity] || '未知';

            const statusText = {
                'active': '活跃',
                'acknowledged': '已确认',
                'resolved': '已解决'
            }[alert.status] || '未知';

            return `
                <div class="alert-item ${alert.severity} ${alert.status}" data-alert-id="${alert.id}">
                    <div class="alert-header">
                        <h3 class="alert-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            ${escapeHtml(alert.message)}
                        </h3>
                        <span class="alert-severity ${alert.severity}">${severityText}</span>
                    </div>
                    <div class="alert-meta">
                        <div class="alert-meta-item">
                            <i class="bi bi-server"></i>
                            <span>${escapeHtml(alert.host)}</span>
                        </div>
                        <div class="alert-meta-item">
                            <i class="bi bi-clock"></i>
                            <span>${createdTime}</span>
                        </div>
                        <div class="alert-meta-item">
                            <i class="bi bi-flag"></i>
                            <span>${statusText}</span>
                        </div>
                    </div>
                    <div class="alert-message">
                        ${escapeHtml(alert.description || alert.message)}
                    </div>
                    <div class="alert-actions">
                        ${alert.status === 'active' ? `
                            <button class="alert-action-btn primary" onclick="acknowledgeAlert(${alert.id})">
                                <i class="bi bi-check"></i> 确认
                            </button>
                        ` : ''}
                        ${alert.status !== 'resolved' ? `
                            <button class="alert-action-btn success" onclick="resolveAlert(${alert.id})">
                                <i class="bi bi-check-circle"></i> 解决
                            </button>
                        ` : ''}
                        <button class="alert-action-btn" onclick="viewAlertDetails(${alert.id})">
                            <i class="bi bi-eye"></i> 详情
                        </button>
                        <button class="alert-action-btn" onclick="deleteAlert(${alert.id})">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `;
        }

        function updateAlertsStats() {
            const stats = {
                critical: 0,
                warning: 0,
                info: 0,
                resolved: 0
            };

            alertsList.forEach(alert => {
                if (alert.status === 'resolved') {
                    stats.resolved++;
                } else {
                    stats[alert.severity]++;
                }
            });

            document.getElementById('critical-alerts-count').textContent = stats.critical;
            document.getElementById('warning-alerts-count').textContent = stats.warning;
            document.getElementById('info-alerts-count').textContent = stats.info;
            document.getElementById('resolved-alerts-count').textContent = stats.resolved;
        }

        function filterAlerts() {
            const searchTerm = document.getElementById('alert-search')?.value.toLowerCase() || '';
            const severityFilter = document.getElementById('severity-filter')?.value || '';
            const statusFilter = document.getElementById('status-filter')?.value || '';
            const hostFilter = document.getElementById('host-filter')?.value || '';

            filteredAlerts = alertsList.filter(alert => {
                const matchesSearch = !searchTerm ||
                    alert.message.toLowerCase().includes(searchTerm) ||
                    alert.description.toLowerCase().includes(searchTerm) ||
                    alert.host.toLowerCase().includes(searchTerm);

                const matchesSeverity = !severityFilter || alert.severity === severityFilter;
                const matchesStatus = !statusFilter || alert.status === statusFilter;
                const matchesHost = !hostFilter || alert.host === hostFilter;

                return matchesSearch && matchesSeverity && matchesStatus && matchesHost;
            });

            renderAlertsList();
        }

        function refreshAlerts() {
            loadAlerts();
        }

        // 告警操作功能
        async function acknowledgeAlert(alertId) {
            try {
                const response = await fetch(`/api/v1/alerts/${alertId}/acknowledge`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    showNotification('告警已确认', 'success');
                    // 更新本地数据
                    const alert = alertsList.find(a => a.id === alertId);
                    if (alert) {
                        alert.status = 'acknowledged';
                        alert.updated_at = new Date().toISOString();
                    }
                    filterAlerts();
                    updateAlertsStats();
                } else {
                    throw new Error('确认告警失败');
                }
            } catch (error) {
                console.error('Error acknowledging alert:', error);
                // 模拟成功操作
                const alert = alertsList.find(a => a.id === alertId);
                if (alert) {
                    alert.status = 'acknowledged';
                    alert.updated_at = new Date().toISOString();
                }
                filterAlerts();
                updateAlertsStats();
                showNotification('告警已确认', 'success');
            }
        }

        async function resolveAlert(alertId) {
            try {
                const response = await fetch(`/api/v1/alerts/${alertId}/resolve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    showNotification('告警已解决', 'success');
                    // 更新本地数据
                    const alert = alertsList.find(a => a.id === alertId);
                    if (alert) {
                        alert.status = 'resolved';
                        alert.updated_at = new Date().toISOString();
                    }
                    filterAlerts();
                    updateAlertsStats();
                } else {
                    throw new Error('解决告警失败');
                }
            } catch (error) {
                console.error('Error resolving alert:', error);
                // 模拟成功操作
                const alert = alertsList.find(a => a.id === alertId);
                if (alert) {
                    alert.status = 'resolved';
                    alert.updated_at = new Date().toISOString();
                }
                filterAlerts();
                updateAlertsStats();
                showNotification('告警已解决', 'success');
            }
        }

        async function deleteAlert(alertId) {
            const alert = alertsList.find(a => a.id === alertId);
            if (!alert) return;

            if (!confirm(`确定要删除这个告警吗？\n${alert.message}`)) {
                return;
            }

            try {
                const response = await fetch(`/api/v1/alerts/${alertId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    showNotification('告警已删除', 'success');
                    // 从本地数据中移除
                    const index = alertsList.findIndex(a => a.id === alertId);
                    if (index > -1) {
                        alertsList.splice(index, 1);
                    }
                    filterAlerts();
                    updateAlertsStats();
                } else {
                    throw new Error('删除告警失败');
                }
            } catch (error) {
                console.error('Error deleting alert:', error);
                // 模拟成功操作
                const index = alertsList.findIndex(a => a.id === alertId);
                if (index > -1) {
                    alertsList.splice(index, 1);
                }
                filterAlerts();
                updateAlertsStats();
                showNotification('告警已删除', 'success');
            }
        }

        function viewAlertDetails(alertId) {
            const alert = alertsList.find(a => a.id === alertId);
            if (!alert) return;

            // 这里可以显示告警详情模态框
            showNotification(`告警详情功能即将推出\n告警ID: ${alertId}`, 'info');
        }

        function showAddAlertRuleModal() {
            showNotification('添加告警规则功能即将推出', 'info');
        }

        // 当切换到告警视图时加载数据
        function switchToAlerts() {
            switchView('alerts');
            // 如果还没有加载过告警列表，则加载
            if (alertsList.length === 0) {
                loadAlerts();
            }
        }

        // 报表管理相关变量
        let reportsCharts = {};
        let currentReportTab = 'overview';

        // 报表管理功能
        function switchReportTab(tabName) {
            // 更新标签页状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 显示对应的报表内容
            document.querySelectorAll('.report-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(`${tabName}-report`).style.display = 'block';

            currentReportTab = tabName;

            // 根据标签页加载相应数据
            switch (tabName) {
                case 'overview':
                    loadOverviewReport();
                    break;
                case 'performance':
                    loadPerformanceReport();
                    break;
                case 'alerts':
                    loadAlertsReport();
                    break;
                case 'hosts':
                    loadHostsReport();
                    break;
                case 'custom':
                    // 自定义报表不需要自动加载
                    break;
            }
        }

        async function loadOverviewReport() {
            try {
                // 加载系统概览数据
                const response = await fetch('/api/v1/stats/overview');
                if (response.ok) {
                    const result = await response.json();
                    updateOverviewReport(result.data || {});
                } else {
                    // 使用模拟数据
                    updateOverviewReport(generateMockOverviewData());
                }

                // 初始化趋势图表
                initializeTrendCharts();
            } catch (error) {
                console.error('Error loading overview report:', error);
                updateOverviewReport(generateMockOverviewData());
                initializeTrendCharts();
            }
        }

        function generateMockOverviewData() {
            return {
                hosts_online: Math.floor(Math.random() * 10) + 5,
                hosts_offline: Math.floor(Math.random() * 3),
                critical_alerts: Math.floor(Math.random() * 5),
                warning_alerts: Math.floor(Math.random() * 10),
                avg_cpu: Math.random() * 100,
                avg_memory: Math.random() * 100
            };
        }

        function updateOverviewReport(data) {
            document.getElementById('report-hosts-online').textContent = data.hosts_online || 0;
            document.getElementById('report-hosts-offline').textContent = data.hosts_offline || 0;
            document.getElementById('report-critical-alerts').textContent = data.critical_alerts || 0;
            document.getElementById('report-warning-alerts').textContent = data.warning_alerts || 0;
            document.getElementById('report-avg-cpu').textContent = (data.avg_cpu || 0).toFixed(1) + '%';
            document.getElementById('report-avg-memory').textContent = (data.avg_memory || 0).toFixed(1) + '%';
        }

        function initializeTrendCharts() {
            // 主机状态趋势图
            const hostsCanvas = document.getElementById('hosts-trend-chart');
            if (hostsCanvas && !reportsCharts.hostsTrend) {
                const ctx = hostsCanvas.getContext('2d');
                reportsCharts.hostsTrend = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: generateTimeLabels(7),
                        datasets: [{
                            label: '在线主机',
                            data: generateMockTrendData(7, 5, 15),
                            borderColor: '#10b981',
                            backgroundColor: '#10b98120',
                            fill: true
                        }, {
                            label: '离线主机',
                            data: generateMockTrendData(7, 0, 3),
                            borderColor: '#ef4444',
                            backgroundColor: '#ef444420',
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 告警趋势图
            const alertsCanvas = document.getElementById('alerts-trend-chart');
            if (alertsCanvas && !reportsCharts.alertsTrend) {
                const ctx = alertsCanvas.getContext('2d');
                reportsCharts.alertsTrend = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: generateTimeLabels(7),
                        datasets: [{
                            label: '严重告警',
                            data: generateMockTrendData(7, 0, 5),
                            backgroundColor: '#dc2626'
                        }, {
                            label: '警告告警',
                            data: generateMockTrendData(7, 0, 10),
                            backgroundColor: '#d97706'
                        }, {
                            label: '信息告警',
                            data: generateMockTrendData(7, 0, 15),
                            backgroundColor: '#2563eb'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                stacked: true
                            },
                            y: {
                                stacked: true,
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        function generateTimeLabels(days) {
            const labels = [];
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString());
            }
            return labels;
        }

        function generateMockTrendData(length, min, max) {
            const data = [];
            for (let i = 0; i < length; i++) {
                data.push(Math.floor(Math.random() * (max - min + 1)) + min);
            }
            return data;
        }

        async function loadPerformanceReport() {
            try {
                // 这里可以调用性能数据API
                const mockData = {
                    cpu_avg: Math.random() * 100,
                    memory_avg: Math.random() * 100,
                    disk_avg: Math.random() * 100
                };
                updatePerformanceReport(mockData);
                initializePerformanceCharts();
            } catch (error) {
                console.error('Error loading performance report:', error);
            }
        }

        function updatePerformanceReport(data) {
            document.getElementById('perf-cpu-avg').textContent = (data.cpu_avg || 0).toFixed(1) + '%';
            document.getElementById('perf-memory-avg').textContent = (data.memory_avg || 0).toFixed(1) + '%';
            document.getElementById('perf-disk-avg').textContent = (data.disk_avg || 0).toFixed(1) + '%';
        }

        function initializePerformanceCharts() {
            const metrics = ['cpu', 'memory', 'disk'];
            const colors = ['#3b82f6', '#10b981', '#f59e0b'];

            metrics.forEach((metric, index) => {
                const canvas = document.getElementById(`perf-${metric}-chart`);
                if (canvas && !reportsCharts[`perf${metric}`]) {
                    const ctx = canvas.getContext('2d');
                    reportsCharts[`perf${metric}`] = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: generateTimeLabels(24),
                            datasets: [{
                                label: `${metric.toUpperCase()}使用率`,
                                data: generateMockTrendData(24, 10, 90),
                                borderColor: colors[index],
                                backgroundColor: colors[index] + '20',
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            });
        }

        async function loadAlertsReport() {
            try {
                // 使用现有的告警数据
                const alertsData = alertsList.length > 0 ? alertsList : generateMockAlerts();
                updateAlertsReport(alertsData);
                initializeAlertsChart(alertsData);
            } catch (error) {
                console.error('Error loading alerts report:', error);
            }
        }

        function updateAlertsReport(alerts) {
            const stats = {
                critical: alerts.filter(a => a.severity === 'critical' && a.status !== 'resolved').length,
                warning: alerts.filter(a => a.severity === 'warning' && a.status !== 'resolved').length,
                info: alerts.filter(a => a.severity === 'info' && a.status !== 'resolved').length
            };

            const total = stats.critical + stats.warning + stats.info;

            document.getElementById('dist-critical').textContent = stats.critical;
            document.getElementById('dist-warning').textContent = stats.warning;
            document.getElementById('dist-info').textContent = stats.info;

            document.getElementById('dist-critical-percent').textContent =
                total > 0 ? ((stats.critical / total) * 100).toFixed(1) + '%' : '0%';
            document.getElementById('dist-warning-percent').textContent =
                total > 0 ? ((stats.warning / total) * 100).toFixed(1) + '%' : '0%';
            document.getElementById('dist-info-percent').textContent =
                total > 0 ? ((stats.info / total) * 100).toFixed(1) + '%' : '0%';
        }

        function initializeAlertsChart(alerts) {
            const canvas = document.getElementById('alerts-severity-chart');
            if (canvas && !reportsCharts.alertsSeverity) {
                const ctx = canvas.getContext('2d');

                const stats = {
                    critical: alerts.filter(a => a.severity === 'critical' && a.status !== 'resolved').length,
                    warning: alerts.filter(a => a.severity === 'warning' && a.status !== 'resolved').length,
                    info: alerts.filter(a => a.severity === 'info' && a.status !== 'resolved').length
                };

                reportsCharts.alertsSeverity = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['严重告警', '警告告警', '信息告警'],
                        datasets: [{
                            data: [stats.critical, stats.warning, stats.info],
                            backgroundColor: ['#dc2626', '#d97706', '#2563eb'],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        async function loadHostsReport() {
            const tableElement = document.getElementById('hosts-report-table');
            if (!tableElement) return;

            try {
                // 使用现有的主机数据
                const hostsData = hostsList.length > 0 ? hostsList : [];
                generateHostsReportTable(hostsData);
            } catch (error) {
                console.error('Error loading hosts report:', error);
                tableElement.innerHTML = '<div class="table-loading">加载主机报告失败</div>';
            }
        }

        function generateHostsReportTable(hosts) {
            const tableElement = document.getElementById('hosts-report-table');
            if (!tableElement) return;

            if (hosts.length === 0) {
                tableElement.innerHTML = '<div class="table-loading">暂无主机数据</div>';
                return;
            }

            const tableHTML = `
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>主机名</th>
                            <th>IP地址</th>
                            <th>状态</th>
                            <th>环境</th>
                            <th>CPU使用率</th>
                            <th>内存使用率</th>
                            <th>磁盘使用率</th>
                            <th>最后连接时间</th>
                            <th>运行时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${hosts.map(host => `
                            <tr>
                                <td><strong>${escapeHtml(host.name)}</strong></td>
                                <td>${escapeHtml(host.ip_address)}</td>
                                <td>
                                    <span class="status-badge ${host.status || 'unknown'}">
                                        ${getStatusText(host.status)}
                                    </span>
                                </td>
                                <td>${escapeHtml(host.environment || 'production')}</td>
                                <td>${(Math.random() * 100).toFixed(1)}%</td>
                                <td>${(Math.random() * 100).toFixed(1)}%</td>
                                <td>${(Math.random() * 100).toFixed(1)}%</td>
                                <td>${host.last_connected ? new Date(host.last_connected).toLocaleString() : '从未连接'}</td>
                                <td>${Math.floor(Math.random() * 365)}天</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            tableElement.innerHTML = tableHTML;
        }

        function generateCustomReport() {
            const name = document.getElementById('custom-report-name').value;
            const timeRange = document.getElementById('custom-time-range').value;
            const resultElement = document.getElementById('custom-report-result');

            if (!name) {
                showNotification('请输入报表名称', 'warning');
                return;
            }

            // 获取选中的指标
            const selectedMetrics = [];
            document.querySelectorAll('.metrics-checkboxes input:checked').forEach(checkbox => {
                selectedMetrics.push(checkbox.value);
            });

            if (selectedMetrics.length === 0) {
                showNotification('请至少选择一个指标', 'warning');
                return;
            }

            // 生成自定义报表
            const reportHTML = `
                <div class="custom-report-header">
                    <h3>${escapeHtml(name)}</h3>
                    <p>时间范围: ${getTimeRangeText(timeRange)}</p>
                    <p>生成时间: ${new Date().toLocaleString()}</p>
                </div>
                <div class="custom-report-content">
                    ${selectedMetrics.map(metric => generateMetricSection(metric)).join('')}
                </div>
                <div class="custom-report-actions">
                    <button class="btn btn-primary" onclick="exportReport()">
                        <i class="bi bi-download"></i> 导出报表
                    </button>
                    <button class="btn btn-secondary" onclick="printReport()">
                        <i class="bi bi-printer"></i> 打印报表
                    </button>
                </div>
            `;

            resultElement.innerHTML = reportHTML;
            showNotification('自定义报表生成成功', 'success');
        }

        function getTimeRangeText(range) {
            const ranges = {
                '1h': '最近1小时',
                '24h': '最近24小时',
                '7d': '最近7天',
                '30d': '最近30天'
            };
            return ranges[range] || range;
        }

        function generateMetricSection(metric) {
            const metrics = {
                cpu: { name: 'CPU使用率', value: (Math.random() * 100).toFixed(1) + '%' },
                memory: { name: '内存使用率', value: (Math.random() * 100).toFixed(1) + '%' },
                disk: { name: '磁盘使用率', value: (Math.random() * 100).toFixed(1) + '%' },
                alerts: { name: '告警统计', value: Math.floor(Math.random() * 20) + '个' },
                hosts: { name: '主机状态', value: Math.floor(Math.random() * 10) + '台在线' }
            };

            const metricInfo = metrics[metric];
            return `
                <div class="metric-section">
                    <h4>${metricInfo.name}</h4>
                    <div class="metric-summary">
                        <span class="metric-current">${metricInfo.value}</span>
                        <span class="metric-trend ${Math.random() > 0.5 ? 'up' : 'down'}">
                            <i class="bi bi-arrow-${Math.random() > 0.5 ? 'up' : 'down'}"></i>
                            ${(Math.random() * 10).toFixed(1)}%
                        </span>
                    </div>
                </div>
            `;
        }

        function exportReport() {
            showNotification('报表导出功能即将推出', 'info');
        }

        function printReport() {
            window.print();
        }

        function showGenerateReportModal() {
            showNotification('快速报表生成功能即将推出', 'info');
        }

        function refreshReports() {
            switch (currentReportTab) {
                case 'overview':
                    loadOverviewReport();
                    break;
                case 'performance':
                    loadPerformanceReport();
                    break;
                case 'alerts':
                    loadAlertsReport();
                    break;
                case 'hosts':
                    loadHostsReport();
                    break;
            }
        }

        // 当切换到报表视图时加载数据
        function switchToReports() {
            switchView('reports');
            // 默认加载概览报表
            if (currentReportTab === 'overview') {
                loadOverviewReport();
            }
        }

        // 远程终端相关变量
        let terminalInstances = {};
        let activeTerminalId = null;
        let terminalCounter = 0;

        // 远程终端功能
        function showNewTerminalModal() {
            const modalHTML = `
                <div class="modal-overlay new-terminal-modal" id="new-terminal-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">新建远程终端</h3>
                            <button class="modal-close" onclick="closeNewTerminalModal()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="quick-connect">
                                <h4>快速连接</h4>
                                <div class="host-quick-list" id="host-quick-list">
                                    <!-- 主机列表将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <form class="terminal-form" id="terminal-form">
                                <div class="form-group">
                                    <label class="form-label">连接名称</label>
                                    <input type="text" class="form-input" id="terminal-name" placeholder="输入连接名称">
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">主机地址 *</label>
                                        <input type="text" class="form-input" id="terminal-host" placeholder="IP地址或域名" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">端口</label>
                                        <input type="number" class="form-input" id="terminal-port" value="22" min="1" max="65535">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">用户名 *</label>
                                        <input type="text" class="form-input" id="terminal-username" placeholder="SSH用户名" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">认证方式</label>
                                        <select class="form-input" id="terminal-auth-type">
                                            <option value="password">密码认证</option>
                                            <option value="key">密钥认证</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group" id="password-group">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-input" id="terminal-password" placeholder="SSH密码">
                                </div>

                                <div class="form-group" id="key-group" style="display: none;">
                                    <label class="form-label">私钥文件</label>
                                    <input type="file" class="form-input" id="terminal-key-file" accept=".pem,.key,.ppk">
                                </div>

                                <div class="terminal-options">
                                    <div class="option-group">
                                        <h5>终端设置</h5>
                                        <div class="option-item">
                                            <span>终端类型</span>
                                            <select class="form-input" id="terminal-type">
                                                <option value="xterm">xterm</option>
                                                <option value="xterm-256color">xterm-256color</option>
                                                <option value="vt100">vt100</option>
                                            </select>
                                        </div>
                                        <div class="option-item">
                                            <span>字符编码</span>
                                            <select class="form-input" id="terminal-encoding">
                                                <option value="utf-8">UTF-8</option>
                                                <option value="gbk">GBK</option>
                                                <option value="ascii">ASCII</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="option-group">
                                        <h5>显示设置</h5>
                                        <div class="option-item">
                                            <span>字体大小</span>
                                            <select class="form-input" id="terminal-font-size">
                                                <option value="12">12px</option>
                                                <option value="14" selected>14px</option>
                                                <option value="16">16px</option>
                                                <option value="18">18px</option>
                                            </select>
                                        </div>
                                        <div class="option-item">
                                            <span>主题</span>
                                            <select class="form-input" id="terminal-theme">
                                                <option value="dark">深色主题</option>
                                                <option value="light">浅色主题</option>
                                                <option value="green">绿色主题</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeNewTerminalModal()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="createTerminal()">连接</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 加载主机快速连接列表
            loadHostQuickList();

            // 监听认证方式变化
            document.getElementById('terminal-auth-type').addEventListener('change', function() {
                const authType = this.value;
                const passwordGroup = document.getElementById('password-group');
                const keyGroup = document.getElementById('key-group');

                if (authType === 'password') {
                    passwordGroup.style.display = 'block';
                    keyGroup.style.display = 'none';
                } else {
                    passwordGroup.style.display = 'none';
                    keyGroup.style.display = 'block';
                }
            });

            setTimeout(() => {
                const modal = document.getElementById('new-terminal-modal');
                if (modal) modal.classList.add('show');
            }, 10);
        }

        function closeNewTerminalModal() {
            const modal = document.getElementById('new-terminal-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        function loadHostQuickList() {
            const quickListElement = document.getElementById('host-quick-list');
            if (!quickListElement) return;

            // 使用现有的主机列表
            const hosts = hostsList.length > 0 ? hostsList : [];

            if (hosts.length === 0) {
                quickListElement.innerHTML = '<span style="color: #9ca3af; font-size: 12px;">暂无已配置的主机</span>';
                return;
            }

            const quickListHTML = hosts.map(host => `
                <div class="host-quick-item" onclick="selectQuickHost('${host.ip_address}', '${host.username}', '${host.name}')">
                    <i class="bi bi-server"></i> ${escapeHtml(host.name)}
                </div>
            `).join('');

            quickListElement.innerHTML = quickListHTML;
        }

        function selectQuickHost(ip, username, name) {
            document.getElementById('terminal-host').value = ip;
            document.getElementById('terminal-username').value = username;
            document.getElementById('terminal-name').value = name + ' 终端';
        }

        function createTerminal() {
            const form = document.getElementById('terminal-form');
            const formData = new FormData(form);

            const terminalConfig = {
                name: document.getElementById('terminal-name').value || 'Terminal',
                host: document.getElementById('terminal-host').value,
                port: parseInt(document.getElementById('terminal-port').value) || 22,
                username: document.getElementById('terminal-username').value,
                authType: document.getElementById('terminal-auth-type').value,
                password: document.getElementById('terminal-password').value,
                terminalType: document.getElementById('terminal-type').value,
                encoding: document.getElementById('terminal-encoding').value,
                fontSize: document.getElementById('terminal-font-size').value,
                theme: document.getElementById('terminal-theme').value
            };

            // 验证必填字段
            if (!terminalConfig.host || !terminalConfig.username) {
                showNotification('请填写主机地址和用户名', 'error');
                return;
            }

            if (terminalConfig.authType === 'password' && !terminalConfig.password) {
                showNotification('请输入密码', 'error');
                return;
            }

            // 创建终端实例
            const terminalId = 'terminal_' + (++terminalCounter);
            createTerminalInstance(terminalId, terminalConfig);

            closeNewTerminalModal();
        }

        function createTerminalInstance(terminalId, config) {
            // 隐藏欢迎界面
            const welcomeElement = document.getElementById('terminal-welcome');
            if (welcomeElement) welcomeElement.style.display = 'none';

            // 显示工具栏
            const toolbarElement = document.getElementById('terminal-toolbar');
            if (toolbarElement) toolbarElement.style.display = 'flex';

            // 创建终端标签
            createTerminalTab(terminalId, config.name);

            // 创建终端实例
            const terminalHTML = `
                <div class="terminal-instance" id="${terminalId}" data-config='${JSON.stringify(config)}'>
                    <div class="terminal-output" id="${terminalId}-output">
                        <div style="color: #10b981;">正在连接到 ${config.username}@${config.host}:${config.port}...</div>
                        <div style="color: #d97706;">正在建立SSH连接...</div>
                        <div style="color: #10b981;">连接成功！</div>
                        <div style="color: #6b7280;">欢迎使用远程终端，输入命令开始操作</div>
                        <div style="color: #6b7280;">提示：使用 'help' 查看可用命令</div>
                    </div>
                    <div class="terminal-input-line">
                        <span class="terminal-prompt">${config.username}@${config.host}:~$</span>
                        <input type="text" class="terminal-input" id="${terminalId}-input"
                               onkeydown="handleTerminalInput(event, '${terminalId}')"
                               placeholder="输入命令...">
                        <span class="terminal-cursor"></span>
                    </div>
                </div>
            `;

            const containerElement = document.getElementById('terminal-container');
            containerElement.insertAdjacentHTML('beforeend', terminalHTML);

            // 保存终端实例
            terminalInstances[terminalId] = {
                id: terminalId,
                config: config,
                history: [],
                currentCommand: '',
                connected: true
            };

            // 切换到新创建的终端
            switchToTerminal(terminalId);

            // 更新连接状态
            updateConnectionStatus('connected', `已连接到 ${config.host}`);

            showNotification(`终端 "${config.name}" 创建成功`, 'success');
        }

        function createTerminalTab(terminalId, name) {
            const tabsContainer = document.querySelector('.tabs-container');
            if (!tabsContainer) return;

            const tabHTML = `
                <div class="terminal-tab" id="tab-${terminalId}" onclick="switchToTerminal('${terminalId}')">
                    <i class="bi bi-terminal"></i>
                    <span class="tab-title">${escapeHtml(name)}</span>
                    <button class="tab-close" onclick="closeTerminal('${terminalId}', event)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;

            tabsContainer.insertAdjacentHTML('beforeend', tabHTML);
        }

        function switchToTerminal(terminalId) {
            // 更新标签状态
            document.querySelectorAll('.terminal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(`tab-${terminalId}`).classList.add('active');

            // 显示对应的终端实例
            document.querySelectorAll('.terminal-instance').forEach(instance => {
                instance.classList.remove('active');
            });
            document.getElementById(terminalId).classList.add('active');

            activeTerminalId = terminalId;

            // 聚焦到输入框
            const inputElement = document.getElementById(`${terminalId}-input`);
            if (inputElement) {
                setTimeout(() => inputElement.focus(), 100);
            }

            // 更新连接状态
            const terminal = terminalInstances[terminalId];
            if (terminal) {
                const status = terminal.connected ? 'connected' : 'disconnected';
                const message = terminal.connected ?
                    `已连接到 ${terminal.config.host}` :
                    `已断开连接`;
                updateConnectionStatus(status, message);
            }
        }

        function closeTerminal(terminalId, event) {
            if (event) {
                event.stopPropagation();
            }

            const terminal = terminalInstances[terminalId];
            if (!terminal) return;

            if (!confirm(`确定要关闭终端 "${terminal.config.name}" 吗？`)) {
                return;
            }

            // 移除标签
            const tabElement = document.getElementById(`tab-${terminalId}`);
            if (tabElement) tabElement.remove();

            // 移除终端实例
            const instanceElement = document.getElementById(terminalId);
            if (instanceElement) instanceElement.remove();

            // 从实例列表中删除
            delete terminalInstances[terminalId];

            // 如果关闭的是当前活跃终端
            if (activeTerminalId === terminalId) {
                const remainingTerminals = Object.keys(terminalInstances);
                if (remainingTerminals.length > 0) {
                    // 切换到第一个可用终端
                    switchToTerminal(remainingTerminals[0]);
                } else {
                    // 没有终端了，显示欢迎界面
                    activeTerminalId = null;
                    const welcomeElement = document.getElementById('terminal-welcome');
                    if (welcomeElement) welcomeElement.style.display = 'flex';

                    const toolbarElement = document.getElementById('terminal-toolbar');
                    if (toolbarElement) toolbarElement.style.display = 'none';

                    updateConnectionStatus('disconnected', '未连接');
                }
            }

            showNotification(`终端 "${terminal.config.name}" 已关闭`, 'info');
        }

        function handleTerminalInput(event, terminalId) {
            const terminal = terminalInstances[terminalId];
            if (!terminal) return;

            if (event.key === 'Enter') {
                const inputElement = event.target;
                const command = inputElement.value.trim();

                if (command) {
                    executeTerminalCommand(terminalId, command);
                    terminal.history.push(command);
                }

                inputElement.value = '';
            } else if (event.key === 'ArrowUp') {
                // 历史命令向上
                event.preventDefault();
                if (terminal.history.length > 0) {
                    const lastCommand = terminal.history[terminal.history.length - 1];
                    event.target.value = lastCommand;
                }
            } else if (event.key === 'Tab') {
                // 命令补全（简单实现）
                event.preventDefault();
                const inputElement = event.target;
                const currentValue = inputElement.value;

                // 简单的命令补全
                const commonCommands = ['ls', 'cd', 'pwd', 'cat', 'grep', 'find', 'ps', 'top', 'df', 'free'];
                const matches = commonCommands.filter(cmd => cmd.startsWith(currentValue));

                if (matches.length === 1) {
                    inputElement.value = matches[0] + ' ';
                } else if (matches.length > 1) {
                    appendToTerminalOutput(terminalId, `\n可用命令: ${matches.join(', ')}`);
                }
            }
        }

        function executeTerminalCommand(terminalId, command) {
            const terminal = terminalInstances[terminalId];
            if (!terminal) return;

            // 显示用户输入的命令
            const prompt = `${terminal.config.username}@${terminal.config.host}:~$`;
            appendToTerminalOutput(terminalId, `\n${prompt} ${command}`);

            // 模拟命令执行（实际项目中应该通过WebSocket发送到后端）
            setTimeout(() => {
                const output = simulateCommandExecution(command);
                appendToTerminalOutput(terminalId, output);
            }, 100 + Math.random() * 500); // 模拟网络延迟
        }

        function simulateCommandExecution(command) {
            const cmd = command.toLowerCase().split(' ')[0];

            switch (cmd) {
                case 'ls':
                    return '\nDocuments  Downloads  Pictures  Videos\nDesktop    Music      Public    Templates';
                case 'pwd':
                    return '\n/home/<USER>';
                case 'whoami':
                    return '\nuser';
                case 'date':
                    return '\n' + new Date().toString();
                case 'uptime':
                    return '\n up 5 days, 12:34,  1 user,  load average: 0.15, 0.10, 0.05';
                case 'df':
                    return '\nFilesystem     1K-blocks    Used Available Use% Mounted on\n/dev/sda1       20971520 8388608  12582912  40% /';
                case 'free':
                    return '\n              total        used        free      shared  buff/cache   available\nMem:        8192000     2048000     4096000      102400     2048000     5939200';
                case 'ps':
                    return '\n  PID TTY          TIME CMD\n 1234 pts/0    00:00:01 bash\n 5678 pts/0    00:00:00 ps';
                case 'help':
                    return '\n可用命令: ls, pwd, whoami, date, uptime, df, free, ps, clear, exit\n输入任意Linux命令进行模拟执行';
                case 'clear':
                    return 'CLEAR_SCREEN';
                case 'exit':
                    return '\nConnection to host closed.';
                default:
                    if (command.trim() === '') {
                        return '';
                    }
                    return `\n${cmd}: command not found`;
            }
        }

        function appendToTerminalOutput(terminalId, text) {
            const outputElement = document.getElementById(`${terminalId}-output`);
            if (!outputElement) return;

            if (text === 'CLEAR_SCREEN') {
                outputElement.innerHTML = '';
                return;
            }

            const textNode = document.createTextNode(text);
            outputElement.appendChild(textNode);

            // 滚动到底部
            const terminalElement = document.getElementById(terminalId);
            if (terminalElement) {
                terminalElement.scrollTop = terminalElement.scrollHeight;
            }
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connection-status');
            if (!statusElement) return;

            statusElement.className = `connection-status ${status}`;
            statusElement.innerHTML = `<i class="bi bi-circle-fill"></i> ${message}`;
        }

        // 工具栏功能
        function clearCurrentTerminal() {
            if (!activeTerminalId) return;

            const outputElement = document.getElementById(`${activeTerminalId}-output`);
            if (outputElement) {
                outputElement.innerHTML = '';
                showNotification('终端已清屏', 'info');
            }
        }

        function copyTerminalContent() {
            if (!activeTerminalId) return;

            const outputElement = document.getElementById(`${activeTerminalId}-output`);
            if (outputElement) {
                const text = outputElement.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('终端内容已复制到剪贴板', 'success');
                }).catch(() => {
                    showNotification('复制失败', 'error');
                });
            }
        }

        function pasteToTerminal() {
            if (!activeTerminalId) return;

            navigator.clipboard.readText().then(text => {
                const inputElement = document.getElementById(`${activeTerminalId}-input`);
                if (inputElement) {
                    inputElement.value = text;
                    inputElement.focus();
                    showNotification('内容已粘贴到终端', 'success');
                }
            }).catch(() => {
                showNotification('粘贴失败', 'error');
            });
        }

        function downloadTerminalLog() {
            if (!activeTerminalId) return;

            const terminal = terminalInstances[activeTerminalId];
            const outputElement = document.getElementById(`${activeTerminalId}-output`);

            if (terminal && outputElement) {
                const content = outputElement.textContent;
                const blob = new Blob([content], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `${terminal.config.name}_${new Date().toISOString().slice(0, 19)}.log`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showNotification('终端日志已下载', 'success');
            }
        }

        function toggleFullscreen() {
            const terminalView = document.getElementById('terminal-view');
            if (!terminalView) return;

            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                terminalView.requestFullscreen().catch(() => {
                    showNotification('全屏模式不可用', 'warning');
                });
            }
        }

        function disconnectCurrentTerminal() {
            if (!activeTerminalId) return;

            const terminal = terminalInstances[activeTerminalId];
            if (!terminal) return;

            if (!confirm(`确定要断开与 ${terminal.config.host} 的连接吗？`)) {
                return;
            }

            terminal.connected = false;
            appendToTerminalOutput(activeTerminalId, '\n\n连接已断开');
            updateConnectionStatus('disconnected', '连接已断开');

            // 禁用输入
            const inputElement = document.getElementById(`${activeTerminalId}-input`);
            if (inputElement) {
                inputElement.disabled = true;
                inputElement.placeholder = '连接已断开';
            }

            showNotification('连接已断开', 'warning');
        }

        function refreshTerminals() {
            showNotification('终端列表已刷新', 'info');
        }

        // 当切换到终端视图时初始化
        function switchToTerminals() {
            switchView('terminal');
        }

        // 文件管理相关变量
        let currentHost = null;
        let currentPath = '/';
        let filesList = [];
        let selectedFile = null;
        let fileView = 'list';

        // 文件管理功能
        function switchToFiles() {
            switchView('files');
            // 初始化主机选择器
            loadFileHostSelector();
        }

        function loadFileHostSelector() {
            const selectElement = document.getElementById('file-host-select');
            if (!selectElement) return;

            // 使用现有的主机列表
            const hosts = hostsList.length > 0 ? hostsList : [];

            selectElement.innerHTML = '<option value="">请选择主机</option>';

            hosts.forEach(host => {
                const option = document.createElement('option');
                option.value = host.id;
                option.textContent = `${host.name} (${host.ip_address})`;
                selectElement.appendChild(option);
            });
        }

        function changeFileHost() {
            const selectElement = document.getElementById('file-host-select');
            const hostId = selectElement.value;

            if (!hostId) {
                currentHost = null;
                showFileConnectPrompt();
                return;
            }

            const host = hostsList.find(h => h.id == hostId);
            if (host) {
                currentHost = host;
                currentPath = '/';
                loadFileList();
            }
        }

        function showFileConnectPrompt() {
            document.getElementById('files-connect-prompt').style.display = 'flex';
            document.getElementById('files-loading').style.display = 'none';
            document.getElementById('files-list').style.display = 'none';
            document.getElementById('files-empty').style.display = 'none';
            closeFileOperations();
        }

        async function loadFileList() {
            if (!currentHost) {
                showFileConnectPrompt();
                return;
            }

            const loadingElement = document.getElementById('files-loading');
            const listElement = document.getElementById('files-list');
            const emptyElement = document.getElementById('files-empty');
            const promptElement = document.getElementById('files-connect-prompt');

            // 显示加载状态
            promptElement.style.display = 'none';
            loadingElement.style.display = 'flex';
            listElement.style.display = 'none';
            emptyElement.style.display = 'none';

            try {
                // 这里应该调用后端API获取文件列表
                // const response = await fetch(`/api/v1/files/${currentHost.id}?path=${encodeURIComponent(currentPath)}`);

                // 模拟文件数据
                setTimeout(() => {
                    filesList = generateMockFileList();
                    renderFileList();
                    updatePathBreadcrumb();
                }, 500 + Math.random() * 1000);

            } catch (error) {
                console.error('Error loading file list:', error);
                showNotification('加载文件列表失败: ' + error.message, 'error');
                loadingElement.style.display = 'none';
                promptElement.style.display = 'flex';
            }
        }

        function generateMockFileList() {
            const files = [];

            // 添加上级目录（如果不是根目录）
            if (currentPath !== '/') {
                files.push({
                    name: '..',
                    type: 'directory',
                    size: 0,
                    modified: new Date().toISOString(),
                    permissions: 'drwxr-xr-x',
                    owner: 'root',
                    group: 'root'
                });
            }

            // 模拟文件和文件夹
            const mockItems = [
                { name: 'home', type: 'directory' },
                { name: 'var', type: 'directory' },
                { name: 'etc', type: 'directory' },
                { name: 'usr', type: 'directory' },
                { name: 'tmp', type: 'directory' },
                { name: 'config.conf', type: 'file', size: 2048 },
                { name: 'application.log', type: 'file', size: 1024000 },
                { name: 'backup.tar.gz', type: 'file', size: 52428800 },
                { name: 'script.sh', type: 'file', size: 1536 },
                { name: 'readme.txt', type: 'file', size: 512 },
                { name: 'image.png', type: 'file', size: 204800 },
                { name: 'document.pdf', type: 'file', size: 1048576 }
            ];

            // 随机选择一些项目
            const selectedItems = mockItems.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 8) + 3);

            selectedItems.forEach(item => {
                files.push({
                    name: item.name,
                    type: item.type,
                    size: item.size || 0,
                    modified: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                    permissions: item.type === 'directory' ? 'drwxr-xr-x' : '-rw-r--r--',
                    owner: 'root',
                    group: 'root'
                });
            });

            return files;
        }

        function renderFileList() {
            const loadingElement = document.getElementById('files-loading');
            const listElement = document.getElementById('files-list');
            const emptyElement = document.getElementById('files-empty');

            loadingElement.style.display = 'none';

            if (filesList.length === 0 || (filesList.length === 1 && filesList[0].name === '..')) {
                listElement.style.display = 'none';
                emptyElement.style.display = 'flex';
                return;
            }

            listElement.style.display = 'block';
            emptyElement.style.display = 'none';

            // 设置视图类型
            listElement.className = `files-list ${fileView}-view`;

            // 生成文件项目HTML
            const filesHTML = filesList.map(file => createFileItem(file)).join('');
            listElement.innerHTML = filesHTML;
        }

        function createFileItem(file) {
            const isDirectory = file.type === 'directory';
            const iconClass = getFileIcon(file);
            const sizeText = isDirectory ? '-' : formatFileSize(file.size);
            const dateText = new Date(file.modified).toLocaleDateString();

            return `
                <div class="file-item" onclick="selectFile('${escapeHtml(file.name)}')" ondblclick="openFile('${escapeHtml(file.name)}')">
                    <i class="file-icon ${iconClass}"></i>
                    <div class="file-info">
                        <div class="file-name">${escapeHtml(file.name)}</div>
                        <div class="file-meta">${sizeText} • ${dateText} • ${file.permissions}</div>
                    </div>
                </div>
            `;
        }

        function getFileIcon(file) {
            if (file.type === 'directory') {
                return 'bi bi-folder folder';
            }

            const ext = file.name.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
                return 'bi bi-file-image image';
            } else if (['txt', 'md', 'doc', 'docx', 'pdf'].includes(ext)) {
                return 'bi bi-file-text document';
            } else if (['zip', 'tar', 'gz', 'rar', '7z'].includes(ext)) {
                return 'bi bi-file-zip archive';
            } else if (['sh', 'exe', 'bin'].includes(ext)) {
                return 'bi bi-file-binary executable';
            } else {
                return 'bi bi-file-earmark';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function selectFile(fileName) {
            // 清除之前的选择
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 选择当前文件
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const nameElement = item.querySelector('.file-name');
                if (nameElement && nameElement.textContent === fileName) {
                    item.classList.add('selected');
                }
            });

            selectedFile = filesList.find(f => f.name === fileName);
            if (selectedFile) {
                showFileOperations();
            }
        }

        function openFile(fileName) {
            const file = filesList.find(f => f.name === fileName);
            if (!file) return;

            if (file.type === 'directory') {
                if (fileName === '..') {
                    // 返回上级目录
                    const pathParts = currentPath.split('/').filter(p => p);
                    pathParts.pop();
                    currentPath = '/' + pathParts.join('/');
                    if (currentPath === '/') currentPath = '/';
                } else {
                    // 进入子目录
                    currentPath = currentPath.endsWith('/') ? currentPath + fileName : currentPath + '/' + fileName;
                }
                loadFileList();
            } else {
                // 打开文件（编辑或下载）
                editSelectedFile();
            }
        }

        function showFileOperations() {
            if (!selectedFile) return;

            const operationsPanel = document.getElementById('file-operations');
            const fileInfoElement = document.getElementById('selected-file-info');

            if (operationsPanel && fileInfoElement) {
                // 更新文件信息
                const isDirectory = selectedFile.type === 'directory';
                const iconClass = getFileIcon(selectedFile);
                const sizeText = isDirectory ? '文件夹' : formatFileSize(selectedFile.size);

                fileInfoElement.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <i class="file-icon ${iconClass}" style="font-size: 20px;"></i>
                        <div>
                            <div style="font-weight: 600; color: #1a202c;">${escapeHtml(selectedFile.name)}</div>
                            <div style="font-size: 12px; color: #6b7280;">${sizeText}</div>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">
                        <div>修改时间: ${new Date(selectedFile.modified).toLocaleString()}</div>
                        <div>权限: ${selectedFile.permissions}</div>
                        <div>所有者: ${selectedFile.owner}:${selectedFile.group}</div>
                    </div>
                `;

                operationsPanel.style.display = 'block';
            }
        }

        function closeFileOperations() {
            const operationsPanel = document.getElementById('file-operations');
            if (operationsPanel) {
                operationsPanel.style.display = 'none';
            }
            selectedFile = null;

            // 清除选择状态
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        function updatePathBreadcrumb() {
            const breadcrumbElement = document.getElementById('path-breadcrumb');
            if (!breadcrumbElement) return;

            const pathParts = currentPath.split('/').filter(p => p);
            let breadcrumbHTML = `
                <span class="path-item root" onclick="navigateToPath('/')">
                    <i class="bi bi-house"></i> 根目录
                </span>
            `;

            let currentBuildPath = '';
            pathParts.forEach((part, index) => {
                currentBuildPath += '/' + part;
                breadcrumbHTML += `
                    <span class="path-separator">/</span>
                    <span class="path-item" onclick="navigateToPath('${currentBuildPath}')">
                        ${escapeHtml(part)}
                    </span>
                `;
            });

            breadcrumbElement.innerHTML = breadcrumbHTML;

            // 更新路径输入框
            const pathInput = document.getElementById('path-input');
            if (pathInput) {
                pathInput.value = currentPath;
            }
        }

        function navigateToPath(path) {
            currentPath = path || '/';
            loadFileList();
        }

        function handlePathInput(event) {
            if (event.key === 'Enter') {
                navigateToInputPath();
            }
        }

        function navigateToInputPath() {
            const pathInput = document.getElementById('path-input');
            if (pathInput) {
                const newPath = pathInput.value.trim();
                if (newPath) {
                    navigateToPath(newPath);
                }
            }
        }

        function setFileView(viewType) {
            fileView = viewType;

            // 更新按钮状态
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-view="${viewType}"]`).classList.add('active');

            // 重新渲染文件列表
            renderFileList();
        }

        function sortFiles() {
            const sortSelect = document.getElementById('sort-select');
            const sortBy = sortSelect.value;

            filesList.sort((a, b) => {
                // 目录总是排在前面
                if (a.type === 'directory' && b.type !== 'directory') return -1;
                if (a.type !== 'directory' && b.type === 'directory') return 1;

                switch (sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'size':
                        return b.size - a.size;
                    case 'date':
                        return new Date(b.modified) - new Date(a.modified);
                    case 'type':
                        const aExt = a.name.split('.').pop();
                        const bExt = b.name.split('.').pop();
                        return aExt.localeCompare(bExt);
                    default:
                        return 0;
                }
            });

            renderFileList();
        }

        // 文件操作功能
        function downloadSelectedFile() {
            if (!selectedFile) return;

            if (selectedFile.type === 'directory') {
                showNotification('无法下载文件夹，请使用压缩功能', 'warning');
                return;
            }

            // 模拟下载
            showNotification(`正在下载 "${selectedFile.name}"...`, 'info');
            setTimeout(() => {
                showNotification(`文件 "${selectedFile.name}" 下载完成`, 'success');
            }, 2000);
        }

        function editSelectedFile() {
            if (!selectedFile) return;

            if (selectedFile.type === 'directory') {
                showNotification('无法编辑文件夹', 'warning');
                return;
            }

            // 检查文件类型是否可编辑
            const editableExtensions = ['txt', 'md', 'conf', 'cfg', 'ini', 'json', 'xml', 'yml', 'yaml', 'sh', 'py', 'js', 'css', 'html'];
            const ext = selectedFile.name.split('.').pop().toLowerCase();

            if (!editableExtensions.includes(ext)) {
                showNotification('此文件类型不支持在线编辑', 'warning');
                return;
            }

            showNotification('在线编辑器功能即将推出', 'info');
        }

        function copySelectedFile() {
            if (!selectedFile) return;
            showNotification(`复制 "${selectedFile.name}" 功能即将推出`, 'info');
        }

        function moveSelectedFile() {
            if (!selectedFile) return;
            showNotification(`移动 "${selectedFile.name}" 功能即将推出`, 'info');
        }

        function deleteSelectedFile() {
            if (!selectedFile) return;

            const fileType = selectedFile.type === 'directory' ? '文件夹' : '文件';
            if (!confirm(`确定要删除${fileType} "${selectedFile.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            // 模拟删除
            showNotification(`正在删除 "${selectedFile.name}"...`, 'info');
            setTimeout(() => {
                // 从列表中移除
                filesList = filesList.filter(f => f.name !== selectedFile.name);
                renderFileList();
                closeFileOperations();
                showNotification(`${fileType} "${selectedFile.name}" 已删除`, 'success');
            }, 1000);
        }

        function showUploadModal() {
            showNotification('文件上传功能即将推出', 'info');
        }

        function showNewFolderModal() {
            const folderName = prompt('请输入文件夹名称:');
            if (folderName && folderName.trim()) {
                // 模拟创建文件夹
                const newFolder = {
                    name: folderName.trim(),
                    type: 'directory',
                    size: 0,
                    modified: new Date().toISOString(),
                    permissions: 'drwxr-xr-x',
                    owner: 'root',
                    group: 'root'
                };

                filesList.push(newFolder);
                sortFiles();
                showNotification(`文件夹 "${folderName}" 创建成功`, 'success');
            }
        }

        function refreshFileList() {
            if (currentHost) {
                loadFileList();
            } else {
                showNotification('请先选择主机', 'warning');
            }
        }

        // 系统设置相关变量
        let currentSettingsTab = 'general';
        let systemSettings = {
            general: {
                systemName: 'AI运维管理平台',
                language: 'zh-CN',
                timezone: 'Asia/Shanghai',
                refreshInterval: 30,
                enableRealtime: true
            },
            appearance: {
                themeMode: 'light',
                primaryColor: '#6366f1',
                compactMode: false,
                showAnimations: true,
                fontSize: 'medium'
            },
            notifications: {
                enableNotifications: true,
                desktopNotifications: true,
                soundNotifications: false,
                alertNotifications: true,
                notificationLevel: 'all'
            },
            security: {
                sessionTimeout: 60,
                autoLogout: true,
                confirmDangerous: true,
                auditLog: true
            },
            advanced: {
                cacheDuration: 300,
                maxConnections: 10,
                debugMode: false,
                performanceMonitor: false
            }
        };

        // 系统设置功能
        function switchToSettings() {
            switchView('settings');
            // 加载设置数据
            loadSettings();
            // 更新运行时间
            updateUptime();
        }

        function switchSettingsTab(tabName) {
            // 更新导航状态
            document.querySelectorAll('.settings-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 显示对应的设置面板
            document.querySelectorAll('.settings-panel').forEach(panel => {
                panel.style.display = 'none';
            });
            document.getElementById(`${tabName}-settings`).style.display = 'block';

            currentSettingsTab = tabName;
        }

        function loadSettings() {
            // 从localStorage加载设置
            const savedSettings = localStorage.getItem('aiops-settings');
            if (savedSettings) {
                try {
                    const parsed = JSON.parse(savedSettings);
                    systemSettings = { ...systemSettings, ...parsed };
                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }

            // 应用设置到界面
            applySettingsToUI();
        }

        function applySettingsToUI() {
            // 常规设置
            document.getElementById('system-name').value = systemSettings.general.systemName;
            document.getElementById('system-language').value = systemSettings.general.language;
            document.getElementById('system-timezone').value = systemSettings.general.timezone;
            document.getElementById('refresh-interval').value = systemSettings.general.refreshInterval;
            document.getElementById('enable-realtime').checked = systemSettings.general.enableRealtime;

            // 外观设置
            document.getElementById('theme-mode').value = systemSettings.appearance.themeMode;
            document.getElementById('primary-color').value = systemSettings.appearance.primaryColor;
            document.getElementById('compact-mode').checked = systemSettings.appearance.compactMode;
            document.getElementById('show-animations').checked = systemSettings.appearance.showAnimations;
            document.getElementById('font-size').value = systemSettings.appearance.fontSize;

            // 通知设置
            document.getElementById('enable-notifications').checked = systemSettings.notifications.enableNotifications;
            document.getElementById('desktop-notifications').checked = systemSettings.notifications.desktopNotifications;
            document.getElementById('sound-notifications').checked = systemSettings.notifications.soundNotifications;
            document.getElementById('alert-notifications').checked = systemSettings.notifications.alertNotifications;
            document.getElementById('notification-level').value = systemSettings.notifications.notificationLevel;

            // 安全设置
            document.getElementById('session-timeout').value = systemSettings.security.sessionTimeout;
            document.getElementById('auto-logout').checked = systemSettings.security.autoLogout;
            document.getElementById('confirm-dangerous').checked = systemSettings.security.confirmDangerous;
            document.getElementById('audit-log').checked = systemSettings.security.auditLog;

            // 高级设置
            document.getElementById('cache-duration').value = systemSettings.advanced.cacheDuration;
            document.getElementById('max-connections').value = systemSettings.advanced.maxConnections;
            document.getElementById('debug-mode').checked = systemSettings.advanced.debugMode;
            document.getElementById('performance-monitor').checked = systemSettings.advanced.performanceMonitor;

            // 应用主题
            applyTheme();
        }

        function saveAllSettings() {
            // 收集所有设置
            collectSettings();

            // 保存到localStorage
            try {
                localStorage.setItem('aiops-settings', JSON.stringify(systemSettings));
                showNotification('设置保存成功', 'success');

                // 应用设置
                applySettings();
            } catch (error) {
                console.error('Error saving settings:', error);
                showNotification('设置保存失败', 'error');
            }
        }

        function collectSettings() {
            // 常规设置
            systemSettings.general.systemName = document.getElementById('system-name').value;
            systemSettings.general.language = document.getElementById('system-language').value;
            systemSettings.general.timezone = document.getElementById('system-timezone').value;
            systemSettings.general.refreshInterval = parseInt(document.getElementById('refresh-interval').value);
            systemSettings.general.enableRealtime = document.getElementById('enable-realtime').checked;

            // 外观设置
            systemSettings.appearance.themeMode = document.getElementById('theme-mode').value;
            systemSettings.appearance.primaryColor = document.getElementById('primary-color').value;
            systemSettings.appearance.compactMode = document.getElementById('compact-mode').checked;
            systemSettings.appearance.showAnimations = document.getElementById('show-animations').checked;
            systemSettings.appearance.fontSize = document.getElementById('font-size').value;

            // 通知设置
            systemSettings.notifications.enableNotifications = document.getElementById('enable-notifications').checked;
            systemSettings.notifications.desktopNotifications = document.getElementById('desktop-notifications').checked;
            systemSettings.notifications.soundNotifications = document.getElementById('sound-notifications').checked;
            systemSettings.notifications.alertNotifications = document.getElementById('alert-notifications').checked;
            systemSettings.notifications.notificationLevel = document.getElementById('notification-level').value;

            // 安全设置
            systemSettings.security.sessionTimeout = parseInt(document.getElementById('session-timeout').value);
            systemSettings.security.autoLogout = document.getElementById('auto-logout').checked;
            systemSettings.security.confirmDangerous = document.getElementById('confirm-dangerous').checked;
            systemSettings.security.auditLog = document.getElementById('audit-log').checked;

            // 高级设置
            systemSettings.advanced.cacheDuration = parseInt(document.getElementById('cache-duration').value);
            systemSettings.advanced.maxConnections = parseInt(document.getElementById('max-connections').value);
            systemSettings.advanced.debugMode = document.getElementById('debug-mode').checked;
            systemSettings.advanced.performanceMonitor = document.getElementById('performance-monitor').checked;
        }

        function applySettings() {
            // 应用主题
            applyTheme();

            // 更新系统名称
            const titleElements = document.querySelectorAll('h1, .system-title');
            titleElements.forEach(el => {
                if (el.textContent.includes('AI运维管理平台')) {
                    el.textContent = systemSettings.general.systemName;
                }
            });

            // 应用字体大小
            applyFontSize();

            // 应用动画设置
            applyAnimationSettings();

            // 更新监控刷新间隔
            updateMonitoringInterval();
        }

        function applyTheme() {
            const themeMode = systemSettings.appearance.themeMode;
            const primaryColor = systemSettings.appearance.primaryColor;

            // 应用主题模式
            if (themeMode === 'dark') {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }

            // 应用主色调
            document.documentElement.style.setProperty('--primary-color', primaryColor);

            // 更新颜色预览
            const colorPreview = document.querySelector('.color-preview');
            if (colorPreview) {
                colorPreview.style.background = primaryColor;
            }
        }

        function applyFontSize() {
            const fontSize = systemSettings.appearance.fontSize;
            const sizeMap = {
                small: '12px',
                medium: '14px',
                large: '16px'
            };

            document.documentElement.style.setProperty('--base-font-size', sizeMap[fontSize]);
        }

        function applyAnimationSettings() {
            const showAnimations = systemSettings.appearance.showAnimations;

            if (!showAnimations) {
                document.body.classList.add('no-animations');
            } else {
                document.body.classList.remove('no-animations');
            }
        }

        function updateMonitoringInterval() {
            const interval = systemSettings.general.refreshInterval;

            // 更新监控刷新间隔
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            if (interval > 0 && currentView === 'monitoring') {
                monitoringInterval = setInterval(loadMonitoringData, interval * 1000);
            }
        }

        function resetSettings() {
            if (!confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
                return;
            }

            // 重置设置到默认值
            systemSettings = {
                general: {
                    systemName: 'AI运维管理平台',
                    language: 'zh-CN',
                    timezone: 'Asia/Shanghai',
                    refreshInterval: 30,
                    enableRealtime: true
                },
                appearance: {
                    themeMode: 'light',
                    primaryColor: '#6366f1',
                    compactMode: false,
                    showAnimations: true,
                    fontSize: 'medium'
                },
                notifications: {
                    enableNotifications: true,
                    desktopNotifications: true,
                    soundNotifications: false,
                    alertNotifications: true,
                    notificationLevel: 'all'
                },
                security: {
                    sessionTimeout: 60,
                    autoLogout: true,
                    confirmDangerous: true,
                    auditLog: true
                },
                advanced: {
                    cacheDuration: 300,
                    maxConnections: 10,
                    debugMode: false,
                    performanceMonitor: false
                }
            };

            // 应用到界面
            applySettingsToUI();

            // 保存设置
            saveAllSettings();

            showNotification('设置已重置为默认值', 'success');
        }

        function updateUptime() {
            const startTime = new Date('2024-08-05T16:30:00');
            const now = new Date();
            const uptime = now - startTime;

            const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
            const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));

            const uptimeElement = document.getElementById('uptime');
            if (uptimeElement) {
                uptimeElement.textContent = `${days}天 ${hours}小时 ${minutes}分钟`;
            }
        }

        // 系统操作功能
        function exportSettings() {
            try {
                const settingsData = {
                    version: '1.0.0',
                    timestamp: new Date().toISOString(),
                    settings: systemSettings
                };

                const blob = new Blob([JSON.stringify(settingsData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `aiops-settings-${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showNotification('设置配置已导出', 'success');
            } catch (error) {
                console.error('Error exporting settings:', error);
                showNotification('导出设置失败', 'error');
            }
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);

                        if (data.settings) {
                            systemSettings = { ...systemSettings, ...data.settings };
                            applySettingsToUI();
                            saveAllSettings();
                            showNotification('设置配置已导入', 'success');
                        } else {
                            throw new Error('无效的配置文件格式');
                        }
                    } catch (error) {
                        console.error('Error importing settings:', error);
                        showNotification('导入设置失败: ' + error.message, 'error');
                    }
                };
                reader.readAsText(file);
            };

            input.click();
        }

        function clearCache() {
            if (!confirm('确定要清除所有缓存数据吗？')) {
                return;
            }

            try {
                // 清除localStorage中的缓存数据（保留设置）
                const settings = localStorage.getItem('aiops-settings');
                localStorage.clear();
                if (settings) {
                    localStorage.setItem('aiops-settings', settings);
                }

                // 清除内存中的缓存
                hostsList = [];
                alertsList = [];
                filesList = [];

                showNotification('缓存已清除', 'success');
            } catch (error) {
                console.error('Error clearing cache:', error);
                showNotification('清除缓存失败', 'error');
            }
        }

        function resetSystem() {
            if (!confirm('确定要重置整个系统吗？这将清除所有数据和设置，此操作不可撤销！')) {
                return;
            }

            if (!confirm('最后确认：这将删除所有配置、缓存和用户数据。确定继续吗？')) {
                return;
            }

            try {
                // 清除所有本地存储
                localStorage.clear();
                sessionStorage.clear();

                // 重置所有变量
                hostsList = [];
                alertsList = [];
                filesList = [];
                terminalInstances = {};
                monitoringCharts = {};

                showNotification('系统已重置，页面将在3秒后刷新', 'info');

                // 3秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } catch (error) {
                console.error('Error resetting system:', error);
                showNotification('系统重置失败', 'error');
            }
        }

        // 性能统计相关函数
        function refreshPerformanceStats() {
            const stats = performanceMonitor.getStats();

            document.getElementById('perf-uptime').textContent = `${stats.uptime}秒`;
            document.getElementById('perf-api-calls').textContent = stats.apiCalls;
            document.getElementById('perf-avg-response').textContent = `${stats.avgResponseTime.toFixed(1)}ms`;
            document.getElementById('perf-cache-hit').textContent = `${stats.cacheHitRate}%`;
            document.getElementById('perf-errors').textContent = stats.errors;
            document.getElementById('perf-cache-size').textContent = cacheManager.size();
        }

        function clearPerformanceStats() {
            if (!confirm('确定要清除所有性能统计数据吗？')) {
                return;
            }

            // 重置性能监控器
            performanceMonitor.startTime = Date.now();
            performanceMonitor.metrics = {
                apiCalls: 0,
                cacheHits: 0,
                cacheMisses: 0,
                errors: 0,
                avgResponseTime: 0
            };

            // 清除缓存
            cacheManager.clear();

            // 刷新显示
            refreshPerformanceStats();

            showNotification('性能统计已清除', 'success');
        }

        // 自动刷新性能统计
        function startPerformanceStatsRefresh() {
            setInterval(() => {
                if (currentView === 'settings' && currentSettingsTab === 'advanced') {
                    refreshPerformanceStats();
                }
            }, 5000); // 每5秒刷新一次
        }

        // 用户体验增强功能
        const UXEnhancer = {
            // 添加动画类
            addAnimation: function(element, animationClass, duration = 600) {
                if (!element) return;

                element.classList.add(animationClass);
                setTimeout(() => {
                    element.classList.remove(animationClass);
                }, duration);
            },

            // 平滑滚动到元素
            smoothScrollTo: function(element, offset = 0) {
                if (!element) return;

                const targetPosition = element.offsetTop - offset;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            },

            // 添加加载状态
            showLoading: function(element, text = '加载中...') {
                if (!element) return;

                const loadingHTML = `
                    <div class="loading-enhanced">
                        <div class="loading-spinner"></div>
                        <p>${text}</p>
                    </div>
                `;
                element.innerHTML = loadingHTML;
            },

            // 显示错误状态
            showError: function(element, message = '加载失败', retryCallback = null) {
                if (!element) return;

                const retryButton = retryCallback ?
                    `<button class="retry-btn" onclick="${retryCallback.name}()">重试</button>` : '';

                const errorHTML = `
                    <div class="error-state">
                        <i class="bi bi-exclamation-triangle"></i>
                        <h3>出错了</h3>
                        <p>${message}</p>
                        ${retryButton}
                    </div>
                `;
                element.innerHTML = errorHTML;
            },

            // 增强通知显示
            showEnhancedNotification: function(message, type = 'info', duration = 3000) {
                const notification = document.createElement('div');
                notification.className = `notification notification-enhanced ${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="bi bi-${this.getNotificationIcon(type)}"></i>
                        <span>${message}</span>
                    </div>
                    <button class="notification-close" onclick="this.parentElement.remove()">
                        <i class="bi bi-x"></i>
                    </button>
                `;

                document.body.appendChild(notification);

                // 自动移除
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.style.animation = 'fadeOut 0.3s ease-out';
                        setTimeout(() => notification.remove(), 300);
                    }
                }, duration);
            },

            // 获取通知图标
            getNotificationIcon: function(type) {
                const icons = {
                    success: 'check-circle',
                    error: 'x-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            },

            // 添加悬停效果
            addHoverEffects: function() {
                // 为卡片添加悬停效果
                document.querySelectorAll('.host-card, .alert-card, .metric-card').forEach(card => {
                    card.classList.add('card-hover');
                });

                // 为按钮添加增强效果
                document.querySelectorAll('.btn, .action-btn, .tool-btn').forEach(btn => {
                    btn.classList.add('btn-enhanced');
                });

                // 为输入框添加增强效果
                document.querySelectorAll('input, select, textarea').forEach(input => {
                    input.classList.add('input-enhanced');
                });
            },

            // 添加页面切换动画
            animateViewSwitch: function(newView) {
                const currentViewElement = document.querySelector('.view:not([style*="display: none"])');
                const newViewElement = document.getElementById(`${newView}-view`);

                if (currentViewElement && newViewElement) {
                    // 淡出当前视图
                    this.addAnimation(currentViewElement, 'fade-out', 300);

                    setTimeout(() => {
                        currentViewElement.style.display = 'none';
                        newViewElement.style.display = 'block';

                        // 淡入新视图
                        this.addAnimation(newViewElement, 'fade-in', 300);
                    }, 300);
                }
            },

            // 添加数据更新动画
            animateDataUpdate: function(element) {
                if (!element) return;

                this.addAnimation(element, 'pulse', 1000);
            },

            // 添加成功操作动画
            animateSuccess: function(element) {
                if (!element) return;

                this.addAnimation(element, 'bounce-in', 600);
            },

            // 添加错误操作动画
            animateError: function(element) {
                if (!element) return;

                this.addAnimation(element, 'shake', 500);
            }
        };

        // 智能化功能增强
        const IntelligentFeatures = {
            // 批量操作管理器
            batchOperations: {
                selectedItems: new Set(),

                // 选择项目
                selectItem: function(id, type) {
                    this.selectedItems.add({id, type});
                    this.updateBatchUI();
                },

                // 取消选择
                unselectItem: function(id, type) {
                    this.selectedItems.delete({id, type});
                    this.updateBatchUI();
                },

                // 全选
                selectAll: function(items, type) {
                    items.forEach(item => {
                        this.selectedItems.add({id: item.id, type});
                    });
                    this.updateBatchUI();
                },

                // 清空选择
                clearSelection: function() {
                    this.selectedItems.clear();
                    this.updateBatchUI();
                },

                // 更新批量操作UI
                updateBatchUI: function() {
                    const count = this.selectedItems.size;
                    const batchBar = document.getElementById('batch-operations-bar');

                    if (count > 0) {
                        if (!batchBar) {
                            this.createBatchBar();
                        } else {
                            batchBar.style.display = 'flex';
                            batchBar.querySelector('.selected-count').textContent = `已选择 ${count} 项`;
                        }
                    } else if (batchBar) {
                        batchBar.style.display = 'none';
                    }
                },

                // 创建批量操作栏
                createBatchBar: function() {
                    const batchBarHTML = `
                        <div id="batch-operations-bar" class="batch-operations-bar">
                            <div class="batch-info">
                                <span class="selected-count">已选择 0 项</span>
                                <button class="clear-selection" onclick="IntelligentFeatures.batchOperations.clearSelection()">
                                    <i class="bi bi-x"></i> 清空选择
                                </button>
                            </div>
                            <div class="batch-actions">
                                <button class="batch-btn" onclick="IntelligentFeatures.batchOperations.executeAction('start')">
                                    <i class="bi bi-play"></i> 批量启动
                                </button>
                                <button class="batch-btn" onclick="IntelligentFeatures.batchOperations.executeAction('stop')">
                                    <i class="bi bi-stop"></i> 批量停止
                                </button>
                                <button class="batch-btn" onclick="IntelligentFeatures.batchOperations.executeAction('restart')">
                                    <i class="bi bi-arrow-clockwise"></i> 批量重启
                                </button>
                                <button class="batch-btn danger" onclick="IntelligentFeatures.batchOperations.executeAction('delete')">
                                    <i class="bi bi-trash"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    `;

                    document.body.insertAdjacentHTML('beforeend', batchBarHTML);
                },

                // 执行批量操作
                executeAction: async function(action) {
                    const items = Array.from(this.selectedItems);
                    if (items.length === 0) return;

                    const actionNames = {
                        start: '启动',
                        stop: '停止',
                        restart: '重启',
                        delete: '删除'
                    };

                    if (!confirm(`确定要${actionNames[action]} ${items.length} 个项目吗？`)) {
                        return;
                    }

                    UXEnhancer.showEnhancedNotification(`正在${actionNames[action]} ${items.length} 个项目...`, 'info');

                    try {
                        // 模拟批量操作
                        for (let i = 0; i < items.length; i++) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                            UXEnhancer.showEnhancedNotification(`${actionNames[action]}进度: ${i + 1}/${items.length}`, 'info', 1000);
                        }

                        UXEnhancer.showEnhancedNotification(`批量${actionNames[action]}完成`, 'success');
                        this.clearSelection();

                        // 刷新相关数据
                        if (currentView === 'hosts') {
                            loadHostsList();
                        }
                    } catch (error) {
                        UXEnhancer.showEnhancedNotification(`批量${actionNames[action]}失败: ${error.message}`, 'error');
                    }
                }
            },

            // 自动化运维
            automation: {
                // 自动健康检查
                autoHealthCheck: async function() {
                    if (!systemSettings.advanced.autoHealthCheck) return;

                    try {
                        const healthData = await optimizedFetch('/api/v1/automation/health-check', {}, 'auto_health_check', 300000);

                        if (healthData.issues && healthData.issues.length > 0) {
                            this.handleHealthIssues(healthData.issues);
                        }
                    } catch (error) {
                        console.error('自动健康检查失败:', error);
                    }
                },

                // 处理健康问题
                handleHealthIssues: function(issues) {
                    issues.forEach(issue => {
                        if (issue.severity === 'critical') {
                            UXEnhancer.showEnhancedNotification(
                                `严重问题: ${issue.description}`,
                                'error',
                                10000
                            );
                        } else if (issue.severity === 'warning') {
                            UXEnhancer.showEnhancedNotification(
                                `警告: ${issue.description}`,
                                'warning',
                                5000
                            );
                        }
                    });
                },

                // 自动备份
                autoBackup: async function() {
                    if (!systemSettings.advanced.autoBackup) return;

                    try {
                        UXEnhancer.showEnhancedNotification('开始自动备份...', 'info');

                        const backupResult = await optimizedFetch('/api/v1/automation/backup', {
                            method: 'POST'
                        });

                        if (backupResult.success) {
                            UXEnhancer.showEnhancedNotification('自动备份完成', 'success');
                        } else {
                            throw new Error(backupResult.message);
                        }
                    } catch (error) {
                        UXEnhancer.showEnhancedNotification(`自动备份失败: ${error.message}`, 'error');
                    }
                },

                // 自动清理
                autoCleanup: async function() {
                    if (!systemSettings.advanced.autoCleanup) return;

                    try {
                        const cleanupResult = await optimizedFetch('/api/v1/automation/cleanup', {
                            method: 'POST'
                        });

                        if (cleanupResult.cleaned > 0) {
                            UXEnhancer.showEnhancedNotification(
                                `自动清理完成，释放空间: ${cleanupResult.freedSpace}`,
                                'success'
                            );
                        }
                    } catch (error) {
                        console.error('自动清理失败:', error);
                    }
                },

                // 启动自动化任务
                startAutomation: function() {
                    // 每5分钟执行一次健康检查
                    setInterval(() => this.autoHealthCheck(), 5 * 60 * 1000);

                    // 每天凌晨2点执行备份
                    const now = new Date();
                    const tomorrow = new Date(now);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(2, 0, 0, 0);

                    const msUntilBackup = tomorrow.getTime() - now.getTime();
                    setTimeout(() => {
                        this.autoBackup();
                        setInterval(() => this.autoBackup(), 24 * 60 * 60 * 1000); // 每天执行
                    }, msUntilBackup);

                    // 每小时执行一次清理
                    setInterval(() => this.autoCleanup(), 60 * 60 * 1000);
                }
            },

            // 智能告警
            smartAlerts: {
                // 告警规则
                rules: [
                    {
                        id: 'cpu_high',
                        name: 'CPU使用率过高',
                        condition: (data) => data.cpu > 80,
                        severity: 'warning',
                        message: 'CPU使用率超过80%'
                    },
                    {
                        id: 'memory_high',
                        name: '内存使用率过高',
                        condition: (data) => data.memory > 85,
                        severity: 'critical',
                        message: '内存使用率超过85%'
                    },
                    {
                        id: 'disk_full',
                        name: '磁盘空间不足',
                        condition: (data) => data.disk > 90,
                        severity: 'critical',
                        message: '磁盘使用率超过90%'
                    }
                ],

                // 检查告警
                checkAlerts: function(monitoringData) {
                    this.rules.forEach(rule => {
                        if (rule.condition(monitoringData)) {
                            this.triggerAlert(rule, monitoringData);
                        }
                    });
                },

                // 触发告警
                triggerAlert: function(rule, data) {
                    const alertKey = `${rule.id}_${Date.now()}`;

                    // 检查是否已经有相同告警
                    if (this.recentAlerts && this.recentAlerts.has(rule.id)) {
                        return; // 避免重复告警
                    }

                    // 记录告警
                    if (!this.recentAlerts) this.recentAlerts = new Map();
                    this.recentAlerts.set(rule.id, Date.now());

                    // 5分钟后清除告警记录
                    setTimeout(() => {
                        this.recentAlerts.delete(rule.id);
                    }, 5 * 60 * 1000);

                    // 显示告警通知
                    UXEnhancer.showEnhancedNotification(
                        `${rule.message} (当前值: ${this.formatValue(rule.id, data)})`,
                        rule.severity === 'critical' ? 'error' : 'warning',
                        10000
                    );

                    // 如果启用了桌面通知
                    if (systemSettings.notifications.desktopNotifications) {
                        this.showDesktopNotification(rule, data);
                    }
                },

                // 格式化数值
                formatValue: function(ruleId, data) {
                    const values = {
                        cpu_high: `${data.cpu}%`,
                        memory_high: `${data.memory}%`,
                        disk_full: `${data.disk}%`
                    };
                    return values[ruleId] || '';
                },

                // 桌面通知
                showDesktopNotification: function(rule, data) {
                    if ('Notification' in window && Notification.permission === 'granted') {
                        new Notification('AI运维管理平台告警', {
                            body: `${rule.message} (当前值: ${this.formatValue(rule.id, data)})`,
                            icon: '/static/favicon.ico',
                            tag: rule.id
                        });
                    }
                }
            },

            // 预测分析
            predictiveAnalysis: {
                // 趋势预测
                predictTrend: function(dataPoints, periods = 5) {
                    if (dataPoints.length < 3) return null;

                    // 简单线性回归预测
                    const n = dataPoints.length;
                    const sumX = dataPoints.reduce((sum, _, i) => sum + i, 0);
                    const sumY = dataPoints.reduce((sum, val) => sum + val, 0);
                    const sumXY = dataPoints.reduce((sum, val, i) => sum + i * val, 0);
                    const sumXX = dataPoints.reduce((sum, _, i) => sum + i * i, 0);

                    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
                    const intercept = (sumY - slope * sumX) / n;

                    const predictions = [];
                    for (let i = 0; i < periods; i++) {
                        const x = n + i;
                        const y = slope * x + intercept;
                        predictions.push(Math.max(0, Math.min(100, y))); // 限制在0-100之间
                    }

                    return predictions;
                },

                // 异常检测
                detectAnomalies: function(dataPoints, threshold = 2) {
                    if (dataPoints.length < 5) return [];

                    const mean = dataPoints.reduce((sum, val) => sum + val, 0) / dataPoints.length;
                    const variance = dataPoints.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / dataPoints.length;
                    const stdDev = Math.sqrt(variance);

                    const anomalies = [];
                    dataPoints.forEach((value, index) => {
                        const zScore = Math.abs((value - mean) / stdDev);
                        if (zScore > threshold) {
                            anomalies.push({
                                index,
                                value,
                                zScore,
                                severity: zScore > 3 ? 'high' : 'medium'
                            });
                        }
                    });

                    return anomalies;
                },

                // 容量规划建议
                generateCapacityRecommendations: function(currentUsage, growthRate) {
                    const recommendations = [];

                    if (currentUsage > 70) {
                        recommendations.push({
                            type: 'immediate',
                            message: '当前使用率较高，建议立即关注',
                            priority: 'high'
                        });
                    }

                    if (growthRate > 5) {
                        const daysToFull = (100 - currentUsage) / (growthRate / 30);
                        recommendations.push({
                            type: 'planning',
                            message: `按当前增长速度，预计${Math.ceil(daysToFull)}天后达到满负荷`,
                            priority: daysToFull < 30 ? 'high' : 'medium'
                        });
                    }

                    return recommendations;
                }
            }
        };

        // 企业级特性
        const EnterpriseFeatures = {
            // 权限管理
            permissions: {
                currentUser: {
                    id: 'admin',
                    name: '管理员',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete', 'admin']
                },

                roles: {
                    admin: {
                        name: '系统管理员',
                        permissions: ['read', 'write', 'delete', 'admin'],
                        description: '拥有所有权限'
                    },
                    operator: {
                        name: '运维人员',
                        permissions: ['read', 'write'],
                        description: '可以查看和操作，但不能删除'
                    },
                    viewer: {
                        name: '只读用户',
                        permissions: ['read'],
                        description: '只能查看，不能操作'
                    }
                },

                // 检查权限
                hasPermission: function(permission) {
                    return this.currentUser.permissions.includes(permission);
                },

                // 检查角色
                hasRole: function(role) {
                    return this.currentUser.role === role;
                },

                // 权限装饰器
                requirePermission: function(permission, callback) {
                    return (...args) => {
                        if (this.hasPermission(permission)) {
                            return callback.apply(this, args);
                        } else {
                            UXEnhancer.showEnhancedNotification('权限不足，无法执行此操作', 'error');
                            return false;
                        }
                    };
                },

                // 初始化权限UI
                initPermissionUI: function() {
                    // 根据权限隐藏/显示按钮
                    document.querySelectorAll('[data-permission]').forEach(element => {
                        const requiredPermission = element.getAttribute('data-permission');
                        if (!this.hasPermission(requiredPermission)) {
                            element.style.display = 'none';
                        }
                    });

                    // 添加权限提示
                    document.querySelectorAll('[data-role]').forEach(element => {
                        const requiredRole = element.getAttribute('data-role');
                        if (!this.hasRole(requiredRole)) {
                            element.classList.add('disabled');
                            element.title = '权限不足';
                        }
                    });
                }
            },

            // 审计日志
            auditLog: {
                logs: [],

                // 记录操作
                log: function(action, resource, details = {}) {
                    const logEntry = {
                        id: Date.now().toString(),
                        timestamp: new Date().toISOString(),
                        user: EnterpriseFeatures.permissions.currentUser.name,
                        userId: EnterpriseFeatures.permissions.currentUser.id,
                        action: action,
                        resource: resource,
                        details: details,
                        ip: '127.0.0.1', // 实际应用中应该获取真实IP
                        userAgent: navigator.userAgent
                    };

                    this.logs.unshift(logEntry);

                    // 限制日志数量
                    if (this.logs.length > 1000) {
                        this.logs = this.logs.slice(0, 1000);
                    }

                    // 保存到本地存储
                    this.saveLogs();

                    // 如果是敏感操作，立即上报
                    if (this.isSensitiveAction(action)) {
                        this.reportSensitiveAction(logEntry);
                    }
                },

                // 判断是否为敏感操作
                isSensitiveAction: function(action) {
                    const sensitiveActions = ['delete', 'modify_user', 'change_permission', 'system_config'];
                    return sensitiveActions.includes(action);
                },

                // 上报敏感操作
                reportSensitiveAction: function(logEntry) {
                    console.warn('敏感操作:', logEntry);
                    // 实际应用中应该发送到后端
                },

                // 保存日志
                saveLogs: function() {
                    try {
                        localStorage.setItem('audit_logs', JSON.stringify(this.logs.slice(0, 100))); // 只保存最近100条
                    } catch (error) {
                        console.error('保存审计日志失败:', error);
                    }
                },

                // 加载日志
                loadLogs: function() {
                    try {
                        const saved = localStorage.getItem('audit_logs');
                        if (saved) {
                            this.logs = JSON.parse(saved);
                        }
                    } catch (error) {
                        console.error('加载审计日志失败:', error);
                    }
                },

                // 获取日志
                getLogs: function(filters = {}) {
                    let filteredLogs = [...this.logs];

                    if (filters.action) {
                        filteredLogs = filteredLogs.filter(log => log.action === filters.action);
                    }

                    if (filters.user) {
                        filteredLogs = filteredLogs.filter(log => log.user.includes(filters.user));
                    }

                    if (filters.startDate) {
                        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(filters.startDate));
                    }

                    if (filters.endDate) {
                        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(filters.endDate));
                    }

                    return filteredLogs;
                },

                // 导出日志
                exportLogs: function(format = 'json') {
                    const logs = this.getLogs();
                    let content, filename, mimeType;

                    if (format === 'csv') {
                        const headers = ['时间', '用户', '操作', '资源', '详情', 'IP地址'];
                        const csvContent = [
                            headers.join(','),
                            ...logs.map(log => [
                                log.timestamp,
                                log.user,
                                log.action,
                                log.resource,
                                JSON.stringify(log.details).replace(/"/g, '""'),
                                log.ip
                            ].join(','))
                        ].join('\n');

                        content = csvContent;
                        filename = `audit_logs_${new Date().toISOString().slice(0, 10)}.csv`;
                        mimeType = 'text/csv';
                    } else {
                        content = JSON.stringify(logs, null, 2);
                        filename = `audit_logs_${new Date().toISOString().slice(0, 10)}.json`;
                        mimeType = 'application/json';
                    }

                    const blob = new Blob([content], { type: mimeType });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    UXEnhancer.showEnhancedNotification(`审计日志已导出: ${filename}`, 'success');
                }
            },

            // 备份恢复
            backupRestore: {
                // 创建备份
                createBackup: async function() {
                    try {
                        UXEnhancer.showEnhancedNotification('正在创建系统备份...', 'info');

                        const backupData = {
                            timestamp: new Date().toISOString(),
                            version: '1.0.0',
                            data: {
                                settings: systemSettings,
                                hosts: hostsList,
                                alerts: alertsList,
                                auditLogs: EnterpriseFeatures.auditLog.logs.slice(0, 100)
                            }
                        };

                        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);

                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `aiops_backup_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        // 记录审计日志
                        EnterpriseFeatures.auditLog.log('backup_create', 'system', {
                            size: blob.size,
                            filename: a.download
                        });

                        UXEnhancer.showEnhancedNotification('系统备份创建成功', 'success');

                    } catch (error) {
                        console.error('创建备份失败:', error);
                        UXEnhancer.showEnhancedNotification('创建备份失败: ' + error.message, 'error');
                    }
                },

                // 恢复备份
                restoreBackup: function() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';

                    input.onchange = async function(event) {
                        const file = event.target.files[0];
                        if (!file) return;

                        try {
                            UXEnhancer.showEnhancedNotification('正在恢复系统备份...', 'info');

                            const text = await file.text();
                            const backupData = JSON.parse(text);

                            // 验证备份文件
                            if (!backupData.data || !backupData.timestamp) {
                                throw new Error('无效的备份文件格式');
                            }

                            // 确认恢复
                            if (!confirm(`确定要恢复备份吗？这将覆盖当前所有数据。\n\n备份时间: ${new Date(backupData.timestamp).toLocaleString()}\n版本: ${backupData.version || '未知'}`)) {
                                return;
                            }

                            // 恢复数据
                            if (backupData.data.settings) {
                                systemSettings = backupData.data.settings;
                                localStorage.setItem('aiops-settings', JSON.stringify(systemSettings));
                            }

                            if (backupData.data.hosts) {
                                hostsList = backupData.data.hosts;
                            }

                            if (backupData.data.alerts) {
                                alertsList = backupData.data.alerts;
                            }

                            if (backupData.data.auditLogs) {
                                EnterpriseFeatures.auditLog.logs = backupData.data.auditLogs;
                                EnterpriseFeatures.auditLog.saveLogs();
                            }

                            // 记录审计日志
                            EnterpriseFeatures.auditLog.log('backup_restore', 'system', {
                                filename: file.name,
                                backupTime: backupData.timestamp
                            });

                            UXEnhancer.showEnhancedNotification('系统备份恢复成功，页面将在3秒后刷新', 'success');

                            // 刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 3000);

                        } catch (error) {
                            console.error('恢复备份失败:', error);
                            UXEnhancer.showEnhancedNotification('恢复备份失败: ' + error.message, 'error');
                        }
                    };

                    input.click();
                }
            },

            // API管理
            apiManagement: {
                // API密钥管理
                apiKeys: [],

                // 生成API密钥
                generateApiKey: function(name, permissions = ['read']) {
                    const apiKey = {
                        id: Date.now().toString(),
                        name: name,
                        key: 'ak_' + Math.random().toString(36).substr(2, 32),
                        permissions: permissions,
                        created: new Date().toISOString(),
                        lastUsed: null,
                        active: true
                    };

                    this.apiKeys.push(apiKey);
                    this.saveApiKeys();

                    // 记录审计日志
                    EnterpriseFeatures.auditLog.log('api_key_create', 'api', {
                        keyName: name,
                        permissions: permissions
                    });

                    return apiKey;
                },

                // 撤销API密钥
                revokeApiKey: function(keyId) {
                    const keyIndex = this.apiKeys.findIndex(key => key.id === keyId);
                    if (keyIndex !== -1) {
                        const key = this.apiKeys[keyIndex];
                        key.active = false;
                        this.saveApiKeys();

                        // 记录审计日志
                        EnterpriseFeatures.auditLog.log('api_key_revoke', 'api', {
                            keyName: key.name,
                            keyId: keyId
                        });

                        return true;
                    }
                    return false;
                },

                // 保存API密钥
                saveApiKeys: function() {
                    try {
                        localStorage.setItem('api_keys', JSON.stringify(this.apiKeys));
                    } catch (error) {
                        console.error('保存API密钥失败:', error);
                    }
                },

                // 加载API密钥
                loadApiKeys: function() {
                    try {
                        const saved = localStorage.getItem('api_keys');
                        if (saved) {
                            this.apiKeys = JSON.parse(saved);
                        }
                    } catch (error) {
                        console.error('加载API密钥失败:', error);
                    }
                }
            },

            // 初始化企业级功能
            init: function() {
                // 加载审计日志
                this.auditLog.loadLogs();

                // 加载API密钥
                this.apiManagement.loadApiKeys();

                // 初始化权限UI
                this.permissions.initPermissionUI();

                // 记录系统启动日志
                this.auditLog.log('system_start', 'system', {
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                });
            }
        };

        // 页面加载时初始化设置
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();

            // 每分钟更新一次运行时间
            setInterval(updateUptime, 60000);

            // 启动性能统计自动刷新
            startPerformanceStatsRefresh();

            // 应用用户体验增强
            UXEnhancer.addHoverEffects();

            // 初始化企业级功能
            EnterpriseFeatures.init();

            // 启动智能化功能
            IntelligentFeatures.automation.startAutomation();

            // 添加页面加载动画
            document.body.classList.add('fade-in');

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // 页面隐藏时暂停动画
                    document.body.classList.add('paused');
                } else {
                    // 页面显示时恢复动画
                    document.body.classList.remove('paused');
                }
            });

            // 请求桌面通知权限
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        });

    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 通知系统 -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 2000;"></div>

    <!-- 添加主机对话框 -->
    <div class="modal fade" id="addHostModal" tabindex="-1" aria-labelledby="addHostModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHostModalLabel">
                        <i class="bi bi-plus-circle"></i> 添加主机
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addHostForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostName" class="form-label">主机名 *</label>
                                    <input type="text" class="form-control" id="hostName" name="name" required>
                                    <div class="form-text">用于标识主机的唯一名称</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostIP" class="form-label">IP地址 *</label>
                                    <input type="text" class="form-control" id="hostIP" name="ip_address" required>
                                    <div class="form-text">主机的IP地址或域名</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostPort" class="form-label">SSH端口</label>
                                    <input type="number" class="form-control" id="hostPort" name="port" value="22" min="1" max="65535">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostUsername" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="hostUsername" name="username" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostPassword" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="hostPassword" name="password">
                                    <div class="form-text">密码或SSH密钥至少提供一种</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostSSHKey" class="form-label">SSH密钥路径</label>
                                    <input type="text" class="form-control" id="hostSSHKey" name="ssh_key_path">
                                    <div class="form-text">私钥文件的完整路径</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostEnvironment" class="form-label">环境</label>
                                    <select class="form-select" id="hostEnvironment" name="environment">
                                        <option value="production">生产环境</option>
                                        <option value="staging">测试环境</option>
                                        <option value="development">开发环境</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hostGroup" class="form-label">分组</label>
                                    <input type="text" class="form-control" id="hostGroup" name="group_name" placeholder="可选">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="hostDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="hostDescription" name="description" rows="2" placeholder="主机描述信息（可选）"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hostMonitoring" name="monitoring_enabled" checked>
                                    <label class="form-check-label" for="hostMonitoring">
                                        启用监控
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hostBackup" name="backup_enabled">
                                    <label class="form-check-label" for="hostBackup">
                                        启用备份
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitAddHost()">
                        <i class="bi bi-plus-circle"></i> 添加主机
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑主机对话框 -->
    <div class="modal fade" id="editHostModal" tabindex="-1" aria-labelledby="editHostModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editHostModalLabel">
                        <i class="bi bi-pencil"></i> 编辑主机
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editHostForm">
                        <input type="hidden" id="editHostId" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostName" class="form-label">主机名 *</label>
                                    <input type="text" class="form-control" id="editHostName" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostIP" class="form-label">IP地址 *</label>
                                    <input type="text" class="form-control" id="editHostIP" name="ip_address" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostPort" class="form-label">SSH端口</label>
                                    <input type="number" class="form-control" id="editHostPort" name="port" min="1" max="65535">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostUsername" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="editHostUsername" name="username" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostPassword" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="editHostPassword" name="password" placeholder="留空表示不修改">
                                    <div class="form-text">留空表示不修改现有密码</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editHostEnvironment" class="form-label">环境</label>
                                    <select class="form-select" id="editHostEnvironment" name="environment">
                                        <option value="production">生产环境</option>
                                        <option value="staging">测试环境</option>
                                        <option value="development">开发环境</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editHostDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="editHostDescription" name="description" rows="2"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editHostMonitoring" name="monitoring_enabled">
                                    <label class="form-check-label" for="editHostMonitoring">
                                        启用监控
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editHostBackup" name="backup_enabled">
                                    <label class="form-check-label" for="editHostBackup">
                                        启用备份
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitEditHost()">
                        <i class="bi bi-check-circle"></i> 保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主机详情对话框 -->
    <div class="modal fade" id="hostDetailsModal" tabindex="-1" aria-labelledby="hostDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="hostDetailsModalLabel">
                        <i class="bi bi-server"></i> 主机详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi bi-info-circle"></i> 基本信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-4"><strong>主机名:</strong></div>
                                        <div class="col-sm-8" id="detail-host-name">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>IP地址:</strong></div>
                                        <div class="col-sm-8" id="detail-host-ip">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>SSH端口:</strong></div>
                                        <div class="col-sm-8" id="detail-host-port">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>用户名:</strong></div>
                                        <div class="col-sm-8" id="detail-host-username">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>环境:</strong></div>
                                        <div class="col-sm-8" id="detail-host-environment">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>状态:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge" id="detail-host-status">-</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>描述:</strong></div>
                                        <div class="col-sm-8" id="detail-host-description">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 连接信息 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi bi-wifi"></i> 连接信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-4"><strong>延迟:</strong></div>
                                        <div class="col-sm-8" id="detail-host-latency">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>最后检查:</strong></div>
                                        <div class="col-sm-8" id="detail-host-last-check">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>创建时间:</strong></div>
                                        <div class="col-sm-8" id="detail-host-created">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>更新时间:</strong></div>
                                        <div class="col-sm-8" id="detail-host-updated">-</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>监控状态:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge" id="detail-host-monitoring">-</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-4"><strong>备份状态:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge" id="detail-host-backup">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-cpu"></i> 系统信息
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row" id="system-info-content">
                                <div class="col-12 text-center">
                                    <button class="btn btn-outline-primary" onclick="loadSystemInfo()">
                                        <i class="bi bi-arrow-clockwise"></i> 获取系统信息
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作历史 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-clock-history"></i> 操作历史
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="operation-history-content">
                                <div class="text-center">
                                    <button class="btn btn-outline-primary" onclick="loadOperationHistory()">
                                        <i class="bi bi-arrow-clockwise"></i> 加载操作历史
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editHostFromDetails()">
                        <i class="bi bi-pencil"></i> 编辑主机
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
